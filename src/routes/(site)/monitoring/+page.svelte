<script lang="ts">
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { t } from '$src/lib/stores/i18n';
    import { enhance } from '$app/forms';
    import {
        Dropdown,
        DropdownItem,
        Checkbox,
        Spinner,
        Input,
        Toggle,
        Label,
        Button,
        Drawer,
        CloseButton
    } from 'flowbite-svelte';
    import {
        EditSolid,
        ChevronDownOutline,
        TicketSolid,
        PhoneSolid,
        AdjustmentsHorizontalSolid,
        SearchOutline,
        InfoCircleSolid,
        ArrowRightOutline
    } from 'flowbite-svelte-icons';

    import type { PageData } from './$types';
    import { sineIn } from 'svelte/easing';
    import { TicketService } from '$src/lib/api/features/ticket/tickets.service';
    import TicketTable from '$src/lib/components/tickets/TicketTable.svelte';
    import TicketStatistics from '$src/lib/components/tickets/TicketStatistics.svelte';

    import {formatTimestamp, formatTimestampDMY } from '$lib/utils';
    import { onMount } from 'svelte';

    export let data: PageData;
    
    $: ({ tickets, users, statuses, error, priorities, ticket_topics, loginUser } = data);    
    
    // TODO - Replace name to selectedStatuses
    let selectedStatus = new Set(['All']);
    let selectedMainInterface = 'All';
    let selectedPriority = new Set(['All']);
    let selectedSentiment = new Set(['All']);

    const statusesName = ['All', 'open', 'assigned', 'waiting', 'closed']; //["default", "open", "close", "waiting to be assigned"]
    // TODO - Check this with Backend
    // const mainInterfaceOptions = ["All", "None", "Calling", "Line", "FBMessenger"];
    const mainInterfaceOptions = ['All', 'Undefine', 'LINE'];
    const priorityOptions = ['All', 'Immediately', 'High', 'Medium', 'Low'];
    const sentimentOptions = ['All', 'Positive', 'Neutral', 'Negative'];

    // Declare searchQuery
    let searchQuery = '';
    let viewMyTasks : boolean =  false;
    let ordering = 'status_id__id'; // default ordering (maps to backend field)
    let currentPage = 1;
    let itemsPerPage = 50;
    let totalPages = 1;
    let isLoading = false;

    // Map frontend column names to backend ordering field names
    const columnFieldMap = {
        'id': 'id',
        'status': 'status_id__id', 
        'priority': 'priority__id',
        'customer': 'customer_id__name',
        'agent': 'owner_id__name',
        'updated_ago': 'updated_on',  // Frontend displays 'updated_ago' but sorts by 'updated_on'
        'created_on': 'created_on',
        'sentiment': 'latest_sentiment'
    };

    function toggleViewMyTasks()  {
        viewMyTasks = !viewMyTasks;
        currentPage = 1; 
        fetchTickets();
        console.log('View My Tasks toggled:', viewMyTasks);
    }

    // Add a function to handle column header click
    function handleSort(column: string) {
        // Map frontend column name to backend field name
        const backendField = columnFieldMap[column];
        
        if (ordering === backendField) {
            ordering = '-' + backendField;
        } else if (ordering === '-' + backendField) {
            ordering = backendField;
        } else {
            ordering = backendField; // Default
        }
        
        currentPage = 1; // Reset to first page when sorting changes
        fetchTickets();
    }

    // Refactor ticket fetching to use ordering and filters
    const ticketService = new TicketService();

    async function fetchTickets() {
        if (isLoading) return; // To avoid multiple requests
        isLoading = true;
        
        // Build filters from current state
        const filters = {
            status_name: selectedStatus.has('All') ? '' : Array.from(selectedStatus).join(','),
            priority_name: selectedPriority.has('All') ? '' : Array.from(selectedPriority).join(','),
            sentiment: selectedSentiment.has('All') ? '' : Array.from(selectedSentiment).join(','),
            search: searchQuery.trim() || '',
            page: currentPage,
            my_tickets: viewMyTasks
        };

        console.log('=== FETCH TICKETS DEBUG ===');
        console.log('Current filters:', filters);
        console.log('Current ordering:', ordering);
        console.log('Current page:', currentPage);

        // Get token from data or session
        const token = data?.token || '';
        const response = await ticketService.getTicketsWithFiltersAndOrdering(token, filters, ordering);
        
        console.log('API Response status:', response.res_status);
        console.log('API Response data:', response);
        if (response.tickets?.results) {
            console.log('Number of tickets returned:', response.tickets.results.length);
            console.log('Total count:', response.tickets.count);
        }
        
        if (response.res_status === 200) {
            // Handle paginated response structure
            if (response.tickets.results) {
                tickets = response.tickets.results;
                // Backend appears to use 10 items per page based on actual response
                // Use this for consistent pagination calculation
                const backendPageSize = 10;
                const totalCount = response.tickets.count || 0;
                
                totalPages = Math.ceil(totalCount / backendPageSize);
                
                console.log('Calculated totalPages:', totalPages, 'from totalCount:', totalCount, 'and backendPageSize:', backendPageSize);
                console.log('Results length:', response.tickets.results.length);
                console.log('Has next:', !!response.tickets.next);
            } else {
                // Fallback for non-paginated response
                tickets = response.tickets;
                totalPages = 1;
            }
        } else {
            console.error('Failed to fetch tickets:', response.error_msg);
        }
        
        isLoading = false;
    }

    let pageTitle = 'Tickets';

    function togglePriority(priority) {
        console.log('=== TOGGLE PRIORITY ===');
        console.log('Priority clicked:', priority);
        console.log('Current selectedPriority before:', Array.from(selectedPriority));
        
        if (priority === 'All') {
            selectedPriority = new Set(['All']);
        } else {
            selectedPriority.delete('All');
            if (selectedPriority.has(priority)) {
                selectedPriority.delete(priority);
                if (selectedPriority.size === 0) {
                    selectedPriority.add('All');
                }
            } else {
                selectedPriority.add(priority);
            }
        }
        selectedPriority = new Set(selectedPriority); // Trigger reactivity
        console.log('Current selectedPriority after:', Array.from(selectedPriority));
        
        currentPage = 1; // Reset to first page when filter changes
        fetchTickets(); 
    }

    function toggleSentiment(sentiment) {
        if (sentiment === 'All') {
            selectedSentiment = new Set(['All']);
        } else {
            selectedSentiment.delete('All');
            if (selectedSentiment.has(sentiment)) {
                selectedSentiment.delete(sentiment);
                if (selectedSentiment.size === 0) {
                    selectedSentiment.add('All');
                }
            } else {
                selectedSentiment.add(sentiment);
            }
        }
        selectedSentiment = new Set(selectedSentiment); // Trigger reactivity
        currentPage = 1; // Reset to first page when filter changes
        fetchTickets(); 
    }

    function toggleStatus(status) {
        console.log('=== TOGGLE STATUS ===');
        console.log('Status clicked:', status);
        console.log('Current selectedStatus before:', Array.from(selectedStatus));
        
        if (status === 'All') {
            selectedStatus = new Set(['All']);
        } else {
            selectedStatus.delete('All');
            if (selectedStatus.has(status)) {
                selectedStatus.delete(status);
                if (selectedStatus.size === 0) {
                    selectedStatus.add('All');
                }
            } else {
                selectedStatus.add(status);
            }
        }
        selectedStatus = new Set(selectedStatus); // Trigger reactivity
        console.log('Current selectedStatus after:', Array.from(selectedStatus));
        
        currentPage = 1; // Reset to first page when filter changes
        fetchTickets(); 
    }

    function resetFilters() {
        selectedStatus = new Set(['All']);
        selectedMainInterface = 'All';
        selectedSentiment = new Set(['All']);
        selectedPriority = new Set(['All']);
        // searchQuery = '';
        viewMyTasks = false; // Reset view my tasks filter
        // ordering = 'status_id__id'; 
        currentPage = 1; // Reset to first page when filters are reset
        fetchTickets(); // Trigger fetch when filters are reset
    }

    function capitalize(str: string): string {
        return str
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    let hidden6 = true;
    let transitionParamsRight = {
        x: 320,
        duration: 200,
        easing: sineIn
    };

    // Add logic for date filtering and toggling between daily and total
    let dateFilter = 'total'; // Can be 'total' or 'daily'
    let selectedDate = new Date(); // Today's date for daily filter
    
    // Filter tickets by the selected date (for daily stats)
    function getDailyTickets() {
        return tickets.filter(ticket => {
            const ticketDate = new Date(ticket.date);
            return ticketDate.toDateString() === selectedDate.toDateString();
        });
    }

    // Filter tickets based on status (for total stats)
    function getTicketCount(status) {
        let ticketsToConsider = dateFilter === 'total' ? tickets : getDailyTickets();
        return ticketsToConsider.filter(ticket => ticket.status === status).length;
    }

    // Toggle between daily and total
    function toggleDateFilter() {
        dateFilter = dateFilter === 'total' ? 'daily' : 'total';
    }

    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;
        // Fetch new data for the selected page
        fetchTickets();
    }

    // Handle Enter key press in search box
    function handleSearchKeydown(event: KeyboardEvent) {
        if (event.key === 'Enter') {
            currentPage = 1; // Reset to first page when search is triggered manually
            fetchTickets();
        }
    }

    // Debounced search - trigger search after user stops typing for 500ms
    let searchTimeout: ReturnType<typeof setTimeout>;
    function delayedSearch() {
        if (searchTimeout) clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // Reset to page 1 when search changes
            currentPage = 1;
            fetchTickets();
        }, 500);
    }
    
    $: searchQuery, delayedSearch();

    // Initialize data when component mounts
    onMount(() => {
        fetchTickets();
    });
</script>

<svelte:head>
    <title>{t('tickets')}</title>
</svelte:head>

<div class="flex h-screen bg-white">
    <div class="flex-1 overflow-y-auto bg-white p-8">
        <Breadcrumb aria-label="Default breadcrumb example" class="mb-3">
            <BreadcrumbItem href="/" home>
            <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
            <span class="text-gray-700">{t('tickets')}</span>
            </BreadcrumbItem>
        </Breadcrumb>

        <div class="mb-6">
            <div class="inline-flex flex-row gap-10 items-center">
                <span>
                    <h2 class="text-2xl font-bold">{t('tickets')}</h2>
                    <p class="text-gray-600">{t('tickets_description')}</p>
                </span>
                <Button
                    color={viewMyTasks ? 'dark' : 'none'}
                    class={`${
                        viewMyTasks ? '' : 'hover:bg-gray-100 border'
                    }`}
                    on:click={toggleViewMyTasks}
                >
                    {t('view_my_tickets')}
                </Button>
            </div>
        </div>
                
        
        <!-- Ticket Statistics Cards -->
        <TicketStatistics tickets={tickets} />
        
        <!-- Filters and Search Bar - Improved Layout -->
        <div class="flex flex-col lg:flex-row items-start lg:items-center lg:justify-between mb-6 gap-4">
            <!-- Left side - Filter Buttons -->
            <div class="flex flex-wrap gap-3">
                <!-- Status Filter -->
                <div>
                    <Button 
                        color={!selectedStatus.has('All') ? 'dark' : 'none'}
                        class={`${!selectedStatus.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4" />
                        <span>{t('status')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown class="w-44 p-2 shadow-lg">
                        {#each statusesName as statusName}
                            <div class="p-2 hover:bg-gray-100 rounded">
                                <Checkbox
                                    checked={selectedStatus.has(statusName)}
                                    on:change={() => toggleStatus(statusName)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">
                                        {capitalize(statusName).replace('_', ' ')}
                                    </span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Priority Filter -->
                <div>
                    <Button
                        color={!selectedPriority.has('All') ? 'dark' : 'none'}
                        class={`${!selectedPriority.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`} 
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4" />
                        <span>{t('priority')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown class="w-44 p-2 shadow-lg">
                        {#each priorityOptions as priorityName}
                            <div class="p-2 hover:bg-gray-100 rounded">
                                <Checkbox 
                                    checked={selectedPriority.has(priorityName)} 
                                    on:change={() => togglePriority(priorityName)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">{priorityName.replace("_", " ")}</span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Sentiment Filter -->
                <div>
                    <Button
                        color={!selectedSentiment.has('All') ? 'dark' : 'none'}
                        class={`${!selectedSentiment.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}  
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4" />
                        <span>{t('sentiment')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown class="w-44 p-2 shadow-lg">
                        {#each sentimentOptions as sentimentName}
                            <div class="p-2 hover:bg-gray-100 rounded">
                                <Checkbox
                                    checked={selectedSentiment.has(sentimentName)}
                                    on:change={() => toggleSentiment(sentimentName)}
                                    class="flex items-center"
                                >
                                    <span class="ml-2 text-sm">{sentimentName.replace('_', ' ')}</span>
                                </Checkbox>
                            </div>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Reset Filter -->
                <Button 
                    color="none" 
                    on:click={resetFilters}
                    class="hover:bg-gray-100 border shadow-md"
                >
                    {t('reset_filters')}
                </Button>
            </div>

            <!-- Right side - Search Bar -->
            <div class="relative w-full lg:w-1/3 shadow-md">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <SearchOutline class="w-5 h-5 text-gray-500" />
                </div>
                <Input
                    id="searchBar"
                    type="text"
                    placeholder={t('search_placeholder')}
                    bind:value={searchQuery}
                    on:keydown={handleSearchKeydown}
                    class={`bg-white block w-full pl-10 py-2.5 border rounded-lg
                        focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
                />
            </div>
        </div>
        
        <!-- Loading indicator
        {#if isLoading}
            <div class="flex justify-center items-center py-8">
                <Spinner class="mr-3" size="4" />
                <span>Loading tickets...</span>
            </div>
        {/if} -->
        
        <!-- Ticket Table Component -->
        <TicketTable 
            tickets={tickets}
            {users}
            {statuses}
            {priorities}
            {ticket_topics}
            {loginUser}
            {currentPage}
            {totalPages}
            {updateCurrentPage}
            {ordering}
            {handleSort}
            on:ticketRefresh={fetchTickets}
        />
    </div>
</div>