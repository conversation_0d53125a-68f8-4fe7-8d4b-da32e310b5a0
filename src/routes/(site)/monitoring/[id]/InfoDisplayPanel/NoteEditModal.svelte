<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Textarea } from 'flowbite-svelte';

	export let editModal: boolean = false;
	export let closeModal: () => void;
	export let editNote: { content: string } | null = null;
	export let customerId: string | number | null = null;

	let editNoteForm: HTMLFormElement;

	export let onSuccess: () => void = () => {};

	function handleUpdateButtonClick() {
		editNoteForm.requestSubmit();
	}
</script>

<Modal title="Edit Note" bind:open={editModal} autoclose>
	<form
		bind:this={editNoteForm}
		method="POST"
		enctype="multipart/form-data"
		action="?/update_note"
		use:enhance={() => {
			return async ({ update, result }) => {
				if (result.type === 'success') {
					await update();
					onSuccess();
					// showSuccess = true;
				} else if (result.type === 'failure') {
					// showError = false;
				}
			};
		}}
	>
		<Textarea id="textarea-id" bind:value={editNote.content} rows="3" name="note" type="text" />
		<input type="hidden" name="customerId" value={customerId} />
		<input type="hidden" name="customerNoteId" value={editNote.id} />

		<div class="flex w-full justify-center gap-4">
			<Button color="alternative" on:click={closeModal} class="w-32">Cancel</Button>
			<Button color="blue" class="w-32" on:click={handleUpdateButtonClick}>Update</Button>
		</div>
	</form>
</Modal>
