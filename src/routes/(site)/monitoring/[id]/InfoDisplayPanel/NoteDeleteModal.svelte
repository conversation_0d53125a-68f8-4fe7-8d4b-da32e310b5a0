<script lang="ts">
    import { enhance } from '$app/forms';
	import { Button, Modal } from 'flowbite-svelte';
	import { ExclamationCircleOutline } from 'flowbite-svelte-icons';

	export let deleteModal: boolean = false;
	// export let closeModal: () => void;
	export let deleteNoteId;
	export let customerId: string | number | null = null;

	export let onSuccess: () => void = () => {};

	let deleteForm: HTMLFormElement;

	function handleDeleteButtonClick() {
		deleteForm.requestSubmit();
	}
</script>

<Modal bind:open={deleteModal} size="xs" autoclose>
	<div class="text-center">
		<ExclamationCircleOutline class="mx-auto mb-4 h-12 w-12 text-gray-400 dark:text-gray-200" />
		<form
			bind:this={deleteForm}
			action="?/delete_note"
			method="POST"
			use:enhance={() => {
				return async ({ update, result }) => {
					console.log(result);
					if (result.type === 'success') {
						await update();
						onSuccess();
						// showSuccess = true;
					} else if (result.type === 'failure') {
						// showError = false;
					}
				};
			}}
		>
			<h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
				Are you sure you want to delete?
			</h3>
			<input type="hidden" name="customerId" value={customerId} />
			<input type="hidden" name="deleteNoteId" value={deleteNoteId} />

			<Button color="alternative">No</Button>
			<Button color="red" class="me-2" on:click={handleDeleteButtonClick}>Yes</Button>
		</form>
	</div>
</Modal>
