<script lang="ts">
    import { onMount } from 'svelte';
    import { fetchDashboard, type DashboardData } from '$lib/api/features/dashboard/dashboard';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { t } from '$src/lib/stores/i18n';
    
    let pageTitle = "Dashboard";
    let currentDashboard: DashboardData | null = null;
    let currentTeamDashboard: DashboardData | null = null;
    let error: string = '';
    let teamError: string = '';
    let activeTabId = 'work-quality';
    let activeDashboardId = 1;
    let activeTeamDashboardId = 3;
    let isLoading = false;
    let isTeamLoading = false;
    let isDropdownOpen = false;
    let isTeamDropdownOpen = false;
    let windowWidth: number;
    
    const tabs = [
        { id: 'team-performance', name: 'team_performance' },
        { id: 'work-quality', name: 'work_quality' },
        { id: 'daily-summary', name: 'daily_summary' }
    ];
    
    const workQualityOptions = [
        { id: 1, name: 'Customer Sentiment' },
        { id: 6, name: 'Customer Topic Breakdown' },
        { id: 7, name: 'Usefulness of AI' },
        { id: 8, name: 'Usefulness of AI 2' },
    ];

    const teamPerformanceOptions = [
        { id: 3, name: 'Human Agent Performance Dashboard (All)' },
        { id: 2, name: 'Unresolved Tickets Dashboard' },
        { id: 4, name: 'Human Agent Performance Dashboard (Single)' },
        { id: 5, name: 'Agent Online/Offline Statistics' },
    ];

    async function loadDashboard(id: number, isTeamTab: boolean = false) {
        if (isTeamTab) {
            isTeamLoading = true;
            teamError = '';
        } else {
            isLoading = true;
            error = '';
        }
        
        try {
            const dashboard = await fetchDashboard(id);
            if (isTeamTab) {
                currentTeamDashboard = dashboard;
            } else {
                currentDashboard = dashboard;
            }
        } catch (e) {
            const errorMsg = `${t('failed_to_load_dashboard')} ${id}`;
            if (isTeamTab) {
                teamError = errorMsg;
                currentTeamDashboard = null;
            } else {
                error = errorMsg;
                currentDashboard = null;
            }
        } finally {
            if (isTeamTab) {
                isTeamLoading = false;
            } else {
                isLoading = false;
            }
        }
    }

    async function switchDashboard(id: number) {
        if (id === activeDashboardId) return;
        activeDashboardId = id;
        isDropdownOpen = false;
        await loadDashboard(activeDashboardId, false);
    }

    async function switchTeamDashboard(id: number) {
        if (id === activeTeamDashboardId) return;
        activeTeamDashboardId = id;
        isTeamDropdownOpen = false;
        await loadDashboard(activeTeamDashboardId, true);
    }

    function switchTab(tabId: string) {
        activeTabId = tabId;
        isDropdownOpen = false;
        isTeamDropdownOpen = false;
        
        // Always load the appropriate dashboard when switching tabs
        if (tabId === 'work-quality') {
            if (!currentDashboard) {
                loadDashboard(activeDashboardId, false);
            }
        } else if (tabId === 'team-performance') {
            if (!currentTeamDashboard) {
                loadDashboard(activeTeamDashboardId, true);
            }
        }
    }

    onMount(async () => {
        if (activeTabId === 'work-quality') {
            await loadDashboard(activeDashboardId, false);
        } else if (activeTabId === 'team-performance') {
            await loadDashboard(activeTeamDashboardId, true);
        }
    });

    function handleClickOutside(event: MouseEvent) {
        const target = event.target as HTMLElement;
        if (!target.closest('.dropdown')) {
            isDropdownOpen = false;
            isTeamDropdownOpen = false;
        }
    }
</script>

<svelte:window 
    on:click={handleClickOutside}
    bind:innerWidth={windowWidth}
/>

<svelte:head>
    <title>{t('analytics_dashboard')}</title>
</svelte:head>

<div class="flex h-screen bg-gray-50">
	<div class="w-full h-full bg-white flex flex-col">
        <!-- Header -->
        <div class="bg-white border-b border-gray-200 px-6 py-4">
            <Breadcrumb aria-label="Default breadcrumb example" class="mb-4">
                <BreadcrumbItem href="/" home>
                    <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                    <span class="text-gray-700">{t('analytics_dashboard')}</span>
                </BreadcrumbItem>
            </Breadcrumb>
            
            <h1 class="text-3xl font-bold text-gray-900">{t('analytics_dashboard')}</h1>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-white border-b border-gray-200">
            <nav class="px-6">
                <div class="flex space-x-8">
                    {#each tabs as tab}
                        <button
                            class="py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 {
                                activeTabId === tab.id 
                                    ? 'border-blue-500 text-blue-600' 
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }"
                            on:click={() => switchTab(tab.id)}
                        >
                            <span class="flex items-center space-x-2">
                                <span>{t(tab.name)}</span>
                            </span>
                        </button>
                    {/each}
                </div>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="flex-1 h-full overflow-hidden">
            {#if activeTabId === 'team-performance'}
                <div class="h-full flex flex-col">
                    <!-- Dashboard Selector for Team Performance -->
                    <div class="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-gray-900">{t('team_performance_metrics')}</h2>
                            <div class="relative dropdown">
                                <button 
                                    class="flex items-center space-x-3 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                                    on:click={() => isTeamDropdownOpen = !isTeamDropdownOpen}
                                >
                                    <span class="font-medium">{currentTeamDashboard ? currentTeamDashboard.title : t('select_dashboard')}</span>
                                    <svg class="w-4 h-4 text-gray-500 transition-transform duration-200 {isTeamDropdownOpen ? 'rotate-180' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                
                                {#if isTeamDropdownOpen}
                                    <div class="absolute right-0 z-20 mt-2 w-80 rounded-lg shadow-xl bg-white ring-1 ring-black ring-opacity-5 border border-gray-200 transform transition-all duration-200 ease-out">
                                        <div class="py-2" role="menu">
                                            {#each teamPerformanceOptions as option}
                                                <button
                                                    class="block w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150 {activeTeamDashboardId === option.id ? 'bg-blue-50 text-blue-700 font-medium border-r-2 border-blue-500' : ''}"
                                                    role="menuitem"
                                                    on:click={() => switchTeamDashboard(option.id)}
                                                >
                                                    {option.name}
                                                </button>
                                            {/each}
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Content -->
                    <div class="flex-1 h-full">
                        {#if teamError}
                            <div class="m-6 bg-red-50 border border-red-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                    <p class="text-red-700 font-medium">{teamError}</p>
                                </div>
                            </div>
                        {:else if isTeamLoading}
                            <div class="h-full flex justify-center items-center">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                                    <p class="text-gray-500">{t('loading_dashboard')}</p>
                                </div>
                            </div>
                        {:else if currentTeamDashboard?.dashboard_url}
                            <div class="h-full">
                                <div class="bg-white h-full overflow-hidden">
                                    <div style="height: 100%; overflow: hidden; position: relative;">
                                        <iframe
                                            title={currentTeamDashboard.title}
                                            src={currentTeamDashboard.dashboard_url}
                                            style="width: 100%; height: calc(100% + 80px); position: absolute; top: -80px; left: 0; border: none;"
                                            loading="lazy"
                                            sandbox="allow-scripts allow-same-origin allow-forms"
                                        ></iframe>
                                    </div>
                                </div>
                            </div>
                        {:else}
                            <div class="h-full flex items-center justify-center bg-gray-50">
                                <div class="text-center">
                                    <h2 class="text-xl font-semibold text-gray-900 mb-2">{t('no_dashboard_available')}</h2>
                                    <p class="text-gray-500">{t('select_team_dashboard_message')}</p>
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            {:else if activeTabId === 'work-quality'}
                <div class="h-full flex flex-col">
                    <!-- Dashboard Selector for Work Quality -->
                    <div class="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-gray-900">{t('work_quality_metrics')}</h2>
                            <div class="relative dropdown">
                                <button 
                                    class="flex items-center space-x-3 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                                    on:click={() => isDropdownOpen = !isDropdownOpen}
                                >
                                    <span class="font-medium">{currentDashboard ? currentDashboard.title : t('select_dashboard')}</span>
                                    <svg class="w-4 h-4 text-gray-500 transition-transform duration-200 {isDropdownOpen ? 'rotate-180' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>
                                
                                {#if isDropdownOpen}
                                    <div class="absolute right-0 z-20 mt-2 w-80 rounded-lg shadow-xl bg-white ring-1 ring-black ring-opacity-5 border border-gray-200 transform transition-all duration-200 ease-out">
                                        <div class="py-2" role="menu">
                                            {#each workQualityOptions as option}
                                                <button
                                                    class="block w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150 {activeDashboardId === option.id ? 'bg-blue-50 text-blue-700 font-medium border-r-2 border-blue-500' : ''}"
                                                    role="menuitem"
                                                    on:click={() => switchDashboard(option.id)}
                                                >
                                                    {option.name}
                                                </button>
                                            {/each}
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Content -->
                    <div class="flex-1 h-full">
                        {#if error}
                            <div class="m-6 bg-red-50 border border-red-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                    <p class="text-red-700 font-medium">{error}</p>
                                </div>
                            </div>
                        {:else if isLoading}
                            <div class="h-full flex justify-center items-center">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                                    <p class="text-gray-500">{t('loading_dashboard')}</p>
                                </div>
                            </div>
                        {:else if currentDashboard?.dashboard_url}
                            <div class="h-full">
                                <div class="bg-white h-full overflow-hidden">
                                    <div style="height: 100%; overflow: hidden; position: relative;">
                                        <iframe
                                            title={currentDashboard.title}
                                            src={currentDashboard.dashboard_url}
                                            style="width: 100%; height: calc(100% + 80px); position: absolute; top: -80px; left: 0; border: none;"
                                            loading="lazy"
                                            sandbox="allow-scripts allow-same-origin allow-forms"
                                        ></iframe>
                                    </div>
                                </div>
                            </div>
                        {:else}
                            <div class="h-full flex items-center justify-center bg-gray-50">
                                <div class="text-center">
                                    <h2 class="text-xl font-semibold text-gray-900 mb-2">{t('no_dashboard_available')}</h2>
                                    <p class="text-gray-500">{t('select_dashboard_message')}</p>
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            {:else if activeTabId === 'daily-summary'}
                <div class="h-full flex items-center justify-center bg-gray-50">
                    <div class="text-center">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">{t('daily_summary')}</h2>
                        <p class="text-gray-500 max-w-md">
                            {t('daily_summary_coming_soon')}
                        </p>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>