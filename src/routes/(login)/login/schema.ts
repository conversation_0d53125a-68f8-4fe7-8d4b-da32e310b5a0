import { z } from "zod";

export const formSchema = z.object({
  username: z.string({
    required_error: "Username is required"
  })
    .min(1, "Username is required")
    // .regex(/^[a-zA-Z]+$/, "Username must contain only letters"),
    .regex(/^[a-zA-Z0-9]+$/, "Username must contain only letters and numbers"),
  password: z.string({
    required_error: "Password is required"
  }).min(1, "Password is required.")
}).required();

export type FormSchema = typeof formSchema;

export type Response = {
  form?: {
    id: string;
    valid: boolean,
    posted: boolean,
    errors: object,
    data: {
      username: string,
      password: string
    },
    message: {
      detail?: string,
      status?: string
    }
  }
}