<script lang="ts">
	// import * as Form from '$lib/components/ui/form';
	// import { Input } from '$lib/components/ui/input';
	// import * as Alert from '$lib/components/ui/alert';

	import { formSchema, type FormSchema, type Response } from './schema';
	import SuperDebug, { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { Button, Label, Input, Card, Toggle } from 'flowbite-svelte';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import Fa from 'svelte-fa';
	import { faUser, faKey, faCircleExclamation } from '@fortawesome/free-solid-svg-icons';
	import { Field, Control, FieldErrors } from 'formsnap';
	import { Alert, Spinner } from 'flowbite-svelte';

	export let data: SuperValidated<Infer<FormSchema>>;
	export let response: Response;

	const form = superForm(data, {
		validators: zodClient(formSchema)
	});

	const { form: formData, enhance, delayed } = form;

	// Client-side validation state for username
	let usernameValidationError = '';

	// Username input handler with filtering and validation
	function handleUsernameInput(event: Event) {
		const target = event.target as HTMLInputElement;

		// // Filter out non-alphabetic characters
		// const filteredValue = target.value.replace(/[^a-zA-Z]/g, '');

		// Filter out non-alphanumeric characters 
		const filteredValue = target.value.replace(/[^a-zA-Z0-9]/g, '');

		// Update form data with filtered value
		$formData.username = filteredValue;

		// Clear validation error when user types valid characters
		if (filteredValue === target.value) {
			usernameValidationError = '';
		} else {
			// Show error if invalid characters were filtered out
			usernameValidationError = 'Username must contain only letters and numbers';
		}

		// Update the input value to reflect the filtered value
		target.value = filteredValue;
	}

	// Handle paste events to filter pasted content
	function handleUsernamePaste(event: ClipboardEvent) {
		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';
		// const filteredText = pastedText.replace(/[^a-zA-Z]/g, '');
		// Filter out non-alphanumeric characters 
		const filteredText = pastedText.replace(/[^a-zA-Z0-9]/g, '');

		// Update form data with filtered value
		$formData.username = filteredText;

		// Show validation message if content was filtered
		if (filteredText !== pastedText) {
			usernameValidationError = 'Username must contain only letters and numbers';
		} else {
			usernameValidationError = '';
		}

		// Update the input element directly
		const target = event.target as HTMLInputElement;
		target.value = filteredText;
	}
</script>

<!-- <SuperDebug data={response} /> -->
<!-- {#if response?.form?.message?.status == 'fail'}
	<Alert border>
		<div class="flex items-center gap-3">
			<Fa icon={faCircleExclamation} />
			<span class="text-lg font-medium">Login failed</span>
		</div>
		<p class="mt-2 text-sm">
			{response?.form?.message?.detail}
		</p>
	</Alert>
{/if} -->
<!-- Main Container -->
<!-- <br /> -->

<div class="row text-center">
	<!-- Left Side - Image -->
	<!-- <div class="column">
        <img src="/images/Salmate-Logo-Transparent.png" alt="Salmate Logo" class="w-64 h-64 object-contain">
    </div> -->

	<!-- Right Side - Login -->
	<div class="column">
		<form class="flex flex-col" action="?/login" method="POST" use:enhance>
			<h3 class="dark:text-white mb-5 text-left text-5xl font-bold text-gray-700">Salmate</h3>
			<br />
			<Field {form} name="username">
				<Control let:attrs>
					<Label class="text-left">Username</Label>
					<div class="relative mb-2">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<Fa icon={faUser} class="text-gray-500" />
						</div>
						<Input
							{...attrs}
							type="text"
							bind:value={$formData.username}
							placeholder="Enter your username"
							class="focus:ring-blue ps-10 focus:border-transparent focus:ring-2 {usernameValidationError
								? 'border-red-500 focus:ring-red-500'
								: ''}"
							on:input={handleUsernameInput}
							on:paste={handleUsernamePaste}
							required
							maxlength="20"
						/>
						<div class="absolute -bottom-4 left-0 text-xs text-red-500">
							{#if usernameValidationError}
								<span>{usernameValidationError}</span>
							{:else}
								<FieldErrors />
							{/if}
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<Field {form} name="password">
				<Control let:attrs>
					<Label class="text-left">Password</Label>
					<div class="relative mb-6">
						<div class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3.5">
							<Fa icon={faKey} class="text-gray-500" />
						</div>
						<Input
							{...attrs}
							type="password"
							bind:value={$formData.password}
							placeholder="Enter your password"
							class="focus:ring-blue ps-10 focus:border-transparent focus:ring-2"
							required
						/>
						<div class="absolute -bottom-4 left-0 text-xs text-red-500">
							<FieldErrors />
						</div>
					</div>
				</Control>
			</Field>
			<br />
			<div class="flex flex-col gap-0">
				<Button
					type="submit"
					class="w-full bg-gradient-to-r from-cyan-400 to-sky-500 hover:from-cyan-500 hover:to-sky-600"
				>
					{#if $delayed}
						<Spinner class="me-3" size="4" color="white" /> Logging In
					{:else}
						Login
					{/if}
				</Button>
				{#if response?.form?.message?.status == 'fail'}
					<div class="text-medium text-red-500">
						{response?.form?.message?.detail || 'Invalid username or password'}
					</div>
				{/if}
			</div>
		</form>
	</div>
</div>
