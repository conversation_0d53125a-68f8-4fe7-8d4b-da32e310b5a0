import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { cubicOut } from "svelte/easing";
import type { TransitionConfig } from "svelte/transition";
import { t, language } from '$lib/stores/i18n';
import { get } from 'svelte/store';
import type { Message } from '$lib/types/customer';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

type FlyAndScaleParams = {
	y?: number;
	x?: number;
	start?: number;
	duration?: number;
};

export const flyAndScale = (
	node: Element,
	params: FlyAndScaleParams = { y: -8, x: 0, start: 0.95, duration: 150 }
): TransitionConfig => {
	const style = getComputedStyle(node);
	const transform = style.transform === "none" ? "" : style.transform;

	const scaleConversion = (
		valueA: number,
		scaleA: [number, number],
		scaleB: [number, number]
	) => {
		const [minA, maxA] = scaleA;
		const [minB, maxB] = scaleB;

		const percentage = (valueA - minA) / (maxA - minA);
		const valueB = percentage * (maxB - minB) + minB;

		return valueB;
	};

	const styleToString = (
		style: Record<string, number | string | undefined>
	): string => {
		return Object.keys(style).reduce((str, key) => {
			if (style[key] === undefined) return str;
			return str + `${key}:${style[key]};`;
		}, "");
	};

	return {
		duration: params.duration ?? 200,
		delay: 0,
		css: (t) => {
			const y = scaleConversion(t, [0, 1], [params.y ?? 5, 0]);
			const x = scaleConversion(t, [0, 1], [params.x ?? 0, 0]);
			const scale = scaleConversion(t, [0, 1], [params.start ?? 0.95, 1]);

			return styleToString({
				transform: `${transform} translate3d(${x}px, ${y}px, 0) scale(${scale})`,
				opacity: t
			});
		},
		easing: cubicOut
	};
};

// Format timestamp to local time
// export function formatTimestamp(timestamp: string): string {
//     return new Date(timestamp).toLocaleTimeString([], { 
//         year: 'numeric',
//         month: 'long',
//         day: '2-digit',
//         hour: '2-digit', 
//         minute: '2-digit',
//         second: '2-digit',
//     });
// };

export function formatTimestamp(timestamp: string) {
    const date = new Date(timestamp);
    const lang = get(language)
    const options = {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    };

    const locale = lang === 'th' ? 'th-TH' : 'en-US';

    return date.toLocaleString(locale, options).replace(',', '');
}


export function formatTimestampDMY(timestamp: string) {
    const date = new Date(timestamp);
    const lang = get(language)
    const options = {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
    };

    const locale = lang === 'th' ? 'th-TH' : 'en-US';

    return date.toLocaleString(locale, options).replace(',', '');
}

// export function displayDate(timestamp: string): string {
//     const d = new Date(timestamp);
//     const day   = d.getDate().toString().padStart(2, '0');
//     const month = d.toLocaleString('en-US', { month: 'short' });
//     const year  = d.getFullYear();
//     const hour  = d.getHours().toString().padStart(2, '0');
//     const minute = d.getMinutes().toString().padStart(2, '0');
    
//     // return `${day} ${month} ${year} ${hour}:${minute}`;
//     return {
//         date: `${day} ${month} ${year}`,
//         time: `${hour}:${minute}`
//     };
// }

export function displayDate(timestamp: string): { date: string; time: string } {

    if (!timestamp) {
		return { date: '-', time: '' };
	}

	const d = new Date(timestamp);
	const lang = get(language); // 'en' or 'th'

	const day = d.getDate().toString().padStart(2, '0');
	const month = d.toLocaleString(lang === 'th' ? 'th-TH' : 'en-US', { month: 'short' });
	const year = d.getFullYear();
	const hour = d.getHours().toString().padStart(2, '0');
	const minute = d.getMinutes().toString().padStart(2, '0');

	return {
		date: `${day} ${month} ${year}`,
		time: `${hour}:${minute}`
	};
}

// export function timeAgo(timestamp: string, ticket_status: string): string {
//     const now = new Date();
//     const lastActiveTime = new Date(timestamp);
//     const diffInSeconds = Math.floor((now.getTime() - lastActiveTime.getTime()) / 1000);
//     const diffInMinutes = Math.floor(diffInSeconds / 60);
//     const diffInHours = Math.floor(diffInMinutes / 60);
//     const diffInDays = Math.floor(diffInHours / 24);
//     const diffInWeeks = Math.floor(diffInDays / 7);
//     const diffInMonths = Math.floor(diffInDays / 30); // Approximate
//     const diffInYears = Math.floor(diffInDays / 365); // Approximate

//     // if (ticket_status === 'close') {
//     // 	return 'Closed';
//     // }

//     if (diffInYears > 0) {
//         return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
//     } else if (diffInMonths > 0) {
//         return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
//     } else if (diffInWeeks > 0) {
//         return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
//     } else if (diffInDays > 0) {
//         return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
//     } else if (diffInHours > 0) {
//         return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
//     } else if (diffInMinutes > 0) {
//         return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
//     } else {
//         return 'Just now';
//     }
// }

export function timeAgo(timestamp: string, ticket_status: string): string {
    const now = new Date();
    const lastActiveTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - lastActiveTime.getTime()) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    const diffInWeeks = Math.floor(diffInDays / 7);
    const diffInMonths = Math.floor(diffInDays / 30); // Approximate
    const diffInYears = Math.floor(diffInDays / 365); // Approximate

    const format = (num: number, unitKey: string, pluralKey: string) =>
        `${num} ${t(num === 1 ? unitKey : pluralKey)} ${t('time_ago.ago')}`;

    if (diffInYears > 0) return format(diffInYears, 'time_ago.year', 'time_ago.years');
    if (diffInMonths > 0) return format(diffInMonths, 'time_ago.month', 'time_ago.months');
    if (diffInWeeks > 0) return format(diffInWeeks, 'time_ago.week', 'time_ago.weeks');
    if (diffInDays > 0) return format(diffInDays, 'time_ago.day', 'time_ago.days');
    if (diffInHours > 0) return format(diffInHours, 'time_ago.hour', 'time_ago.hours');
    if (diffInMinutes > 0) return format(diffInMinutes, 'time_ago.minute', 'time_ago.minutes');
    return t('time_ago.just_now');
}



export function getStatusClass(statusId: number): string {
    const statusClasses = {
      2: 'flex item-center justify-center rounded-md bg-green-200 p-2 text-sm text-green-700', // open
      3: 'flex item-center justify-center rounded-md bg-blue-200 p-2 text-sm text-blue-700',   // assigned
      4: 'flex item-center justify-center rounded-md bg-yellow-200 p-2 text-sm text-yellow-700', // waiting
      5: 'flex item-center justify-center rounded-md bg-gray-100 p-2 text-sm text-gray-700',   // pending to close
      6: 'flex item-center justify-center rounded-md bg-gray-100 p-2 text-sm text-gray-700',   // closed
    };
    
    return statusClasses[statusId] || 'flex item-center justify-center rounded-md bg-gray-100 p-2 text-sm text-gray-700';
}

export function getPriorityClass(priorityName: string): string {
    const priorityClasses = {
      'Low': 'flex items-center justify-center rounded-md p-2 bg-gray-100 text-gray-700',
      'Medium': 'flex items-center justify-center rounded-md p-2 bg-yellow-200 text-yellow-700',
      'High': 'flex items-center justify-center rounded-md p-2 bg-orange-200 text-orange-700',
      'Immediately': 'flex items-center justify-center rounded-md p-2 bg-red-200 text-red-700',
    };
    
    return priorityClasses[priorityName] || 'flex items-center justify-center rounded-md p-2 bg-gray-100 text-gray-700';
}
  
export function getSentimentClass(sentiment: string | undefined): string {
    const sentimentClasses = {
      'Positive': 'bg-green-200 text-green-700',
      'Neutral': 'bg-gray-100 text-gray-700',
      'Negative': 'bg-red-200 text-red-700',
    };
    
    return sentimentClasses[sentiment || ''] || 'bg-gray-100 text-gray-700';
}   
  
export function getSentimentIcon(sentiment: string | undefined): string {
    const sentimentIcons = {
      'Positive': '/images/sentiment-positive.png',
      'Neutral': '/images/sentiment-neutral.png',
      'Negative': '/images/sentiment-negative.png',
    };
    
    return sentimentIcons[sentiment || ''] || '/images/sentiment-non.png';
}

export const colorOptions = [
    { name: "red", class: "bg-red-500" },
    { name: "orange", class: "bg-orange-500" },
    { name: "amber", class: "bg-amber-500" },
    { name: "yellow", class: "bg-yellow-500" },
    { name: "pink", class: "bg-pink-500" },
    { name: "purple", class: "bg-purple-400" },
    { name: "violet", class: "bg-violet-500" },
    { name: "blue", class: "bg-blue-500" },
    { name: "cyan", class: "bg-cyan-500" },
    { name: "green", class: "bg-green-500" },
    { name: "lime", class: "bg-lime-500" },
    { name: "gray", class: "bg-gray-500" }
];

export function getColorClass(selectedColor: string): string {
    return colorOptions.find(c => c.name === selectedColor)?.class || 'bg-gray-500';
}

// Enhanced badge configuration functions
export function getStatusBadgeConfig(id: number, status: string) {
    const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
        none: {
            class: '',
            text: '',
            showIcon: false
        },
        closed: {
            class: getStatusClass(id),
            text: t('tickets_closed'),
            showIcon: true
        },
        open: {
            class: getStatusClass(id),
            text: t('tickets_open'),
            showIcon: false
        },
        assigned: {
            class: getStatusClass(id),
            text: t('tickets_assigned'),
            showIcon: false
        },
        waiting: {
            class: getStatusClass(id),
            text: t('tickets_waiting'),
            showIcon: false
        },
        pending_to_close: {
            class: getStatusClass(id),
            text: t('tickets_pending_to_close'),
            showIcon: false
        }
    };
    return configs[status?.toLowerCase()] || configs['none'];
}

export function getPriorityBadgeConfig(priorityName: string) {
    const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
        none: {
            class: '',
            text: '',
            showIcon: false
        },
        Low: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_low'),
            showIcon: false
        },
        Medium: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_medium'),
            showIcon: true
        },
        High: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_high'),
            showIcon: true
        },
        Immediately: {
            class: getPriorityClass(priorityName),
            text: t('tickets_priority_immediately'),
            showIcon: true
        }
    };
    return configs[priorityName] || configs['none'];
}

// Group messages by date
export function groupMessagesByDate(messages: Message[]) {
    const groups: { date: string; messages: Message[] }[] = [];
    let currentDate = '';
    
    messages.forEach(msg => {
        const msgDate = new Date(msg.created_on).toLocaleDateString();
        
        if (msgDate !== currentDate) {
            currentDate = msgDate;
            groups.push({
                date: msgDate,
                messages: [msg]
            });
        } else {
            groups[groups.length - 1].messages.push(msg);
        }
    });
    
    return groups;
}

// Check if should show avatar (first message or different sender)
export function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
    if (index === 0) return true;
    const prevMessage = messages[index - 1];
    return prevMessage.is_self !== message.is_self || 
            prevMessage.user_name !== message.user_name;
}