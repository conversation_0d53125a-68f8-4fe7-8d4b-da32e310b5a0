<script lang="ts">
    import { onDestroy } from 'svelte';
    import { enhance } from '$app/forms';
    import { Toast } from 'flowbite-svelte'; // Import the Toast component
    import { fly } from 'svelte/transition'; // Import the transition
    import {
        CloseCircleSolid,
        CheckCircleSolid
	} from 'flowbite-svelte-icons';
    
    import { t } from '$lib/stores/i18n';

    // Accept the store and any additional props you need
    export let companySettings;

    // Business type options
    const businessTypes = [
        'Insurance',
        // 'Bank'
    ];
    
    // For tracking changes
    let originalValues = {};
    let changedFields = new Set();
    let hasUnsavedChanges = false;
    
    // Toast status and counter
    let showSaveSuccess = false;
    let toastStatus = false;
    let toastMessage = "Changes saved successfully!";
    let counter = 0;

    // Form reference
    let settingsForm;
    
    // Initialize original values when component loads
    $: {
        if ($companySettings && Object.keys($companySettings).length > 0 && Object.keys(originalValues).length === 0) {
            originalValues = { ...$companySettings };
        }
    }
    
    // Track field changes
    function trackChange(field) {
        if ($companySettings[field] !== originalValues[field]) {
            changedFields.add(field);
        } else {
            changedFields.delete(field);
        }
        hasUnsavedChanges = changedFields.size > 0;
    }
    
    // Enhance options for form submission
    const enhanceOptions = {
        pending: () => {
            // Show loading state if needed
        },
        error: () => {
            // Handle errors
            showSaveSuccess = false;
            toastStatus = false;
        },
        success: () => {
            // Handle successful submission
            originalValues = { ...$companySettings };
            changedFields.clear();
            hasUnsavedChanges = false;
            toastMessage = "Changes saved successfully!"; // Update toast message on success
            toastStatus = true;
            counter = 2;
            timeout();
        }
    };
    
    // Handle form submission
    function handleSubmit() {
        // Any additional handling before form submission
    }
    
    // Function to handle form submission
    function saveSettings() {
        // Prepare the data for the form submission
        const settings = [
            // Company settings
            { key: "COMPANY_THAI_NAME", value: $companySettings.thaiName },
            { key: "COMPANY_ENGLISH_NAME", value: $companySettings.englishName },
            { key: "COMPANY_BUSINESS", value: $companySettings.business },
            { key: "COMPANY_BUSINESS_TYPE", value: $companySettings.businessType },
        ];
        
        // Update the hidden input value with the JSON settings
        const settingsInput = settingsForm.querySelector('input[name="settings"]');
        settingsInput.value = JSON.stringify(settings);
        
        toastStatus = true;
        counter = 2;
        timeout();
        
        // Submit the form
        settingsForm.requestSubmit();
    }
    
    // Save changes through the form
    function saveChanges() {
        saveSettings();
        showSaveSuccess = true;
        setTimeout(() => {
            showSaveSuccess = false;
            // Reset unsaved changes message after saving
            hasUnsavedChanges = false;
        }, 3000);
    }
    
    // Countdown timer for toast
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }
    
    // Handle page navigation - reset to original values
    onDestroy(() => {
        if (hasUnsavedChanges) {
            // Reset to original values when navigating away
            $companySettings = { ...originalValues };
        }
    });
</script>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    <!-- Success toast notification using Flowbite Svelte Toast -->
    {#if toastStatus}
         <Toast 
            color="green" 
            transition={fly} 
            params={{ x: 200 }}
            bind:toastStatus
            class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
        >
            <CheckCircleSolid slot="icon" class="h-5 w-5" />
            {toastMessage}
        </Toast>
    {/if}
    

    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-xl font-medium text-gray-700">{t('company_info_title')}</h2>
            <p class="text-sm text-gray-500">{t('company_info_description')}</p>
        </div>
        
        <!-- Save changes button -->
        <button 
            type="button" 
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm 
            {hasUnsavedChanges ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
            on:click={saveChanges}
            disabled={!hasUnsavedChanges}
        >
            {#if hasUnsavedChanges}
                {t('save_changes')}
            {:else}
                {t('save')}
            {/if}
        </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
        <div>
            <label for="thaiName" class="block text-sm font-medium text-gray-700">
                {t('company_name_th')}<!-- Thai Company Name -->
                {#if changedFields.has('thaiName')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <input 
                type="text" 
                id="thaiName" 
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                       {changedFields.has('thaiName') ? 'border-blue-500 bg-blue-50' : ''}" 
                bind:value={$companySettings.thaiName}
                on:input={() => trackChange('thaiName')}
            />
        </div>

        <div>
            <label for="englishName" class="block text-sm font-medium text-gray-700">
                {t('company_name_en')}<!-- English Company Name -->
                {#if changedFields.has('englishName')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <input 
                type="text" 
                id="englishName" 
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                       {changedFields.has('englishName') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$companySettings.englishName}
                on:input={() => trackChange('englishName')}
            />
        </div>

        <div>
            <label for="business" class="block text-sm font-medium text-gray-700">
                {t('company_business')}<!-- Company Business -->
                {#if changedFields.has('business')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <input 
                type="text" 
                id="business" 
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                       {changedFields.has('business') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$companySettings.business}
                on:input={() => trackChange('business')}
            />
        </div>

        <div>
            <label for="businessType" class="block text-sm font-medium text-gray-700">
                {t('business_type')}<!-- Business Type -->
                {#if changedFields.has('businessType')}
                    <span class="text-blue-600 ml-1">({t('modified')})</span>
                {/if}
            </label>
            <select 
                id="businessType" 
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500
                       {changedFields.has('businessType') ? 'border-blue-500 bg-blue-50' : ''}"
                bind:value={$companySettings.businessType}
                on:change={() => trackChange('businessType')}
            >
                <option value="">Select business type</option>
                {#each businessTypes as type}
                    <option value={type}>{type}</option>
                {/each}
            </select>
        </div>
    </div>
    
    <!-- Moved the warning notification to the bottom -->
    {#if hasUnsavedChanges}
        <div class="bg-amber-50 border-l-4 border-amber-400 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <!-- Warning icon -->
                    <svg class="h-5 w-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-amber-700">
                        {t('unsaved_changes')}
                    </p>
                </div>
            </div>
        </div>
    {/if}
    
    <!-- Hidden form for settings submission -->
    <form 
        bind:this={settingsForm} 
        action="?/update_system_setting" 
        method="POST" 
        use:enhance={() => enhanceOptions}
        on:submit={handleSubmit}
        class="hidden"
    >
        <input type="hidden" name="settings" value="">
    </form>
</div>
