 <!-- Systemection.svelte -->
<script lang="ts">
    import { onDestroy } from 'svelte';
    import { enhance } from '$app/forms';
    import { fly } from 'svelte/transition';
    import { Toast, Timeline, TimelineItem, Button, Toggle } from 'flowbite-svelte';
    import {
        CloseCircleSolid,
        CheckCircleSolid
    } from 'flowbite-svelte-icons';
    import { t } from '$lib/stores/i18n';
    import { Dropzone } from 'flowbite-svelte';

    // State variables for transfer settings
    let transferChangedFields = new Set();
    let hasUnsavedTransferChanges = false;
    let originalTransferValues = {};

    export let systemSettings;

    // State variables
    let originalValues = {};
    let colorChangedFields = new Set();
    let logoChanged = false;
    let hasUnsavedColorChanges = false;

    // Toast state
    let toastStatus = false;
    let toastMessage = "Changes saved successfully!";
    let counter = 0;
    let settingsForm;
    let logoForm;
    let uploadingLogo = false;

    // Initialize original values on load
    $: {
        if ($systemSettings && Object.keys($systemSettings).length > 0 && Object.keys(originalValues).length === 0) {
            originalValues = { ...$systemSettings };
            // Initialize transfer settings original values
            originalTransferValues = { 
                transfer_ticket_partner: $systemSettings.transfer_ticket_partner,
                transfer_ticket_department: $systemSettings.transfer_ticket_department
            };
        }
    }
    
    // Track changes to transfer settings
    // function trackTransferChange(field) {
    //     systemSettings.update(settings => {
    //         const updatedSettings = { ...settings };
    //         updatedSettings[field] = field === 'transfer_ticket_department' ? 
    //             transferDepartmentChecked : transferPartnerChecked;
    //         return updatedSettings;
    //     });
        
    //     if ($systemSettings[field] !== originalTransferValues[field]) {
    //         transferChangedFields.add(field);
    //     } else {
    //         transferChangedFields.delete(field);
    //     }
    //     hasUnsavedTransferChanges = transferChangedFields.size > 0;
    // }

    // Track changes to color fields
    function trackColorChange(field) {
        if ($systemSettings[field] !== originalValues[field]) {
            colorChangedFields.add(field);
        } else {
            colorChangedFields.delete(field);
            
        }
        hasUnsavedColorChanges = colorChangedFields.size > 0;
    }

    // File upload handler (input selection)
    function handleFileUpload(event) {
        const file = event.target.files[0];
        processFile(file);
    }

    // File drop handler (drag and drop)
    const dropHandle = (event) => {
        event.preventDefault();
        let file;
        if (event.dataTransfer.items) {
            for (let i = 0; i < event.dataTransfer.items.length; i++) {
                if (event.dataTransfer.items[i].kind === 'file') {
                    file = event.dataTransfer.items[i].getAsFile();
                    break;
                }
            }
        } else {
            file = event.dataTransfer.files[0];
        }

        processFile(file);
    };

    // Process file method to centralize file handling
    function processFile(file) {
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                systemSettings.update(settings => {
                    return { ...settings, logo: e.target.result };
                });
                logoChanged = true;
            };
            reader.readAsDataURL(file);
        }
    }

    // Remove logo method
    function removeLogo() {
        systemSettings.update(settings => {
            return { ...settings, logo: null };
        });
        logoChanged = true;
    }

    // Save color settings method
    function saveColorSettings() {
        const settings = [
            { key: "DOMINANT_COLOR", value: $systemSettings.dominant_color },
            { key: "SECONDARY_COLOR", value: $systemSettings.secondary_color },
            { key: "ACCENT_COLOR", value: $systemSettings.accent_color },
        ];

        const settingsInput = settingsForm.querySelector('input[name="settings"]');
        settingsInput.value = JSON.stringify(settings);

        toastMessage = "Color settings saved successfully!";
        toastStatus = true;
        counter = 2;
        timeout();

        settingsForm.requestSubmit();
    }

    // Manually handle form submission for logo upload
    async function saveLogo() {
        if (uploadingLogo) return;
        
        if ($systemSettings.logo && logoChanged) {
            uploadingLogo = true;
            toastMessage = "Logo saving...";
            toastStatus = true;
            counter = 2;
            timeout();
            
            try {
                // Get the base64 data
                const base64Data = $systemSettings.logo;
                
                // Convert base64 to blob
                const fetchRes = await fetch(base64Data);
                const blob = await fetchRes.blob();
                
                // Create a new FormData and append the file
                const formData = new FormData();
                formData.append('image_file', blob, 'logo.png');
                formData.append('key', 'COMPANY_LOGO');
                
                // Manual fetch instead of using the form
                const response = await fetch('?/upload_image', {
                    method: 'POST',
                    body: formData,
                });
                
                const result = await response.json();
                
                logoChanged = false;
                toastMessage = "Logo saved successfully!";
                                
            } catch (error) {
                console.error('Logo upload error:', error);
                toastMessage = `Error: ${error.message || "Failed to upload logo"}`;
            } finally {
                uploadingLogo = false;
                toastStatus = true;
                counter = 2;
                timeout();
            }
        } else if (logoChanged && !$systemSettings.logo) {
            // Handle logo removal
            uploadingLogo = true;
            toastMessage = "Removing logo...";
            toastStatus = true;
            counter = 2;
            timeout();
            
            try {
                // Create a blank file to represent logo removal
                const formData = new FormData();
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
                formData.append('key', 'LOGO');
                
                // Manual fetch
                const response = await fetch('?/upload_image', {
                    method: 'POST',
                    body: formData,
                });
                
                const result = await response.json();

                logoChanged = false;
                toastMessage = "Logo removed successfully!";
            } catch (error) {
                console.error('Logo removal error:', error);
                toastMessage = `Error: ${error.message || "Failed to remove logo"}`;
            } finally {
                uploadingLogo = false;
                toastStatus = true;
                counter = 2;
                timeout();
            }
        }
    }

    // Save transfer settings method
    // function saveTransferSettings() {
    //     const settings = [
    //         { key: "TRANSFER_TICKET_PARTNER", value: $systemSettings.transfer_ticket_partner ? "1" : "0" },
    //         { key: "TRANSFER_TICKET_DEPARTMENT", value: $systemSettings.transfer_ticket_department ? "1" : "0" },
    //     ];

    //     const settingsInput = settingsForm.querySelector('input[name="settings"]');
    //     settingsInput.value = JSON.stringify(settings);

    //     toastMessage = "Transfer settings saved successfully!";
    //     toastStatus = true;
    //     counter = 2;
    //     timeout();

    //     settingsForm.requestSubmit();
    // }

    // Toast timeout method
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }

    // Preserve original values if component is destroyed with unsaved changes
    onDestroy(() => {
        if (hasUnsavedColorChanges || logoChanged) {
            $systemSettings = { ...originalValues };
        }
    });

    // Fix for the toggle controls - convert string values to boolean
    $: transferDepartmentChecked = $systemSettings.transfer_ticket_department === 'true' || $systemSettings.transfer_ticket_department === true;
    $: transferPartnerChecked = $systemSettings.transfer_ticket_partner === 'true' || $systemSettings.transfer_ticket_partner === true;


    // Add these variables to the existing script section
    let csatImageChanged = false;
    let uploadingCsatImage = false;
    let csatImageForm;

    // File upload handler for CSAT image
    function handleCsatImageUpload(event) {
        const file = event.target.files[0];
        processCsatImageFile(file);
    }

    // File drop handler for CSAT image
    const handleCsatImageDrop = (event) => {
        event.preventDefault();
        let file;
        if (event.dataTransfer.items) {
            for (let i = 0; i < event.dataTransfer.items.length; i++) {
                if (event.dataTransfer.items[i].kind === 'file') {
                    file = event.dataTransfer.items[i].getAsFile();
                    break;
                }
            }
        } else {
            file = event.dataTransfer.files[0];
        }

        processCsatImageFile(file);
    };

    // Process CSAT image file method
    function processCsatImageFile(file) {
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                systemSettings.update(settings => {
                    return { ...settings, csat_image: e.target.result };
                });
                csatImageChanged = true;
            };
            reader.readAsDataURL(file);
        }
    }

    // Remove CSAT image method
    function removeCsatImage() {
        systemSettings.update(settings => {
            return { ...settings, csat_image: null };
        });
        csatImageChanged = true;
    }

    // Save CSAT image method
    async function saveCsatImage() {
        if (uploadingCsatImage) return;
        
        if ($systemSettings.csat_image && csatImageChanged) {
            uploadingCsatImage = true;
            toastMessage = "CSAT image saving...";
            toastStatus = true;
            counter = 2;
            timeout();
            
            try {
                // Get the base64 data
                const base64Data = $systemSettings.csat_image;
                
                // Convert base64 to blob
                const fetchRes = await fetch(base64Data);
                const blob = await fetchRes.blob();
                
                // Create a new FormData and append the file
                const formData = new FormData();
                formData.append('image_file', blob, 'csat_image.png');
                formData.append('key', 'LINE_CSAT');
                
                // Manual fetch for upload
                const response = await fetch('?/upload_image', {
                    method: 'POST',
                    body: formData,
                });
                
                const result = await response.json();
                
                csatImageChanged = false;
                toastMessage = "CSAT image saved successfully!";
                                
            } catch (error) {
                console.error('CSAT image upload error:', error);
                toastMessage = `Error: ${error.message || "Failed to upload CSAT image"}`;
            } finally {
                uploadingCsatImage = false;
                toastStatus = true;
                counter = 2;
                timeout();
            }
        } else if (csatImageChanged && !$systemSettings.csat_image) {
            // Handle CSAT image removal
            uploadingCsatImage = true;
            toastMessage = "Removing CSAT image...";
            toastStatus = true;
            counter = 2;
            timeout();
            
            try {
                // Create a blank file to represent CSAT image removal
                const formData = new FormData();
                formData.append('image_file', new Blob([''], { type: 'text/plain' }), 'empty.txt');
                formData.append('key', 'LINE_CSAT');
                
                // Manual fetch
                const response = await fetch('?/upload_image', {
                    method: 'POST',
                    body: formData,
                });
                
                const result = await response.json();

                csatImageChanged = false;
                toastMessage = "CSAT image removed successfully!";
            } catch (error) {
                console.error('CSAT image removal error:', error);
                toastMessage = `Error: ${error.message || "Failed to remove CSAT image"}`;
            } finally {
                uploadingCsatImage = false;
                toastStatus = true;
                counter = 2;
                timeout();
            }
        }
    }
</script>

<!-- Reduced space-y from 6 to 4 -->
<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    <!-- Toast notification -->
    {#if toastStatus}
        <Toast 
            color="green" 
            transition={fly} 
            params={{ x: 200 }}
            bind:toastStatus
            class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
        >
            <CheckCircleSolid slot="icon" class="h-5 w-5" />
            {toastMessage}
        </Toast>
    {/if}

    <div>
        <h2 class="text-xl font-medium text-gray-700">{t('appearance_title')}</h2>
        <p class="text-sm text-gray-500">{t('appearance_description')}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Logo Upload -->
        <div>
            <div class="flex justify-between items-center mb-2">
                <label class="block text-sm font-medium text-gray-700">
                    <!-- Upload Logo  -->
                    {t('upload_logo')}
                    {#if logoChanged}
                        <span class="text-blue-600 ml-1">
                            ({t('modified')})
                        </span>
                    {/if}
                </label>
                
                <button 
                    type="button" 
                    class="inline-flex items-center px-3 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm 
                        {logoChanged ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
                    on:click={saveLogo}
                    disabled={!logoChanged || uploadingLogo}
                >
                    {#if uploadingLogo}
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('saving')}
                    {:else}
                        {t('save_logo')}
                    {/if}
                </button>
            </div>
            
            <!-- Removed aspect-square here -->
            <div class="mt-1 relative w-full">
                {#if $systemSettings.logo}
                    <div class="relative">
                        <!-- Changed h-64 to h-auto -->
                        <img 
                            src={$systemSettings.logo} 
                            alt="Logo preview" 
                            class="w-full h-auto object-contain" 
                        />
                        <button 
                            on:click={removeLogo}
                            class="absolute top-0 right-0 bg-white rounded-full p-1 shadow-md -mt-2 -mr-2"
                            title="Remove logo"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                {/if}

                <!-- Removed aspect-square from Dropzone; replaced with fixed h-64 for a controlled area -->
                <Dropzone
                    id="logo-dropzone"
                    class="w-full h-64 border-dashed border-2 border-gray-300 flex flex-col items-center justify-center 
                           { $systemSettings.logo ? 'hidden' : '' }"
                    on:drop={dropHandle}
                    on:dragover={(event) => event.preventDefault()}
                >
                    <svg aria-hidden="true" class="mb-3 w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p class="mb-2 text-sm text-gray-500">
                        <span class="font-semibold">
                            {t('click_and_drag')}
                        </span> 
                        <!-- or drag and drop -->
                    </p>
                    <p class="text-xs text-gray-500">
                        {t('recommended_size')}: 480px × 480px
                    </p>
                    <input id="logo-upload" type="file" accept="image/*" class="hidden" on:change={handleFileUpload} />
                </Dropzone>
            </div>

            <div class="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                <p class="text-xs text-blue-700">
                    <strong>{t('note')}:</strong> {t('upload_logo_note')}
                </p>
            </div>
        </div>

        <!-- Colors Section -->
        <div>
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-sm font-medium text-gray-700">
                    <!-- Color Settings -->
                    {t('color_settings')}

                </h3>
                
                <button 
                    type="button" 
                    class="inline-flex items-center px-3 py-3 border border-transparent font-medium rounded-md shadow-sm 
                        {hasUnsavedColorChanges ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
                    on:click={saveColorSettings}
                    disabled={!hasUnsavedColorChanges}
                >
                    <!-- Save Colors -->
                     {t('save_colors')}
                </button>
            </div>

            <!-- Dominant Color -->
            <label for="dominant-color" class="block text-sm font-medium text-gray-700">
                {t('dominant_color')} 
                {#if colorChangedFields.has('dominant_color')}
                    <span class="text-blue-600 ml-1">
                        ({t('modified')})
                    </span>
                {/if}
            </label>
            <div class="mt-1 flex items-center">
                <input 
                    id="dominant-color-picker"
                    type="color" 
                    class="h-8 w-8 border border-gray-300 rounded cursor-pointer" 
                    bind:value={$systemSettings.dominant_color}
                    on:input={() => trackColorChange('dominant_color')}
                    aria-labelledby="dominant-color"
                />
                <input 
                    id="dominant-color"
                    type="text" 
                    class="ml-2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                    bind:value={$systemSettings.dominant_color}
                    on:input={() => trackColorChange('dominant_color')}
                />
            </div>
            <p class="mt-1 text-sm text-gray-500">{t('dominant_color_desc')}</p>

            <!-- Secondary Color -->
            <label for="secondary-color" class="block text-sm font-medium text-gray-700 mt-4">
                {t('secondary_color')} {#if colorChangedFields.has('secondary_color')}<span class="text-blue-600 ml-1">(t{'modified'})</span>{/if}
            </label>
            <div class="mt-1 flex items-center">
                <input 
                    id="secondary-color-picker"
                    type="color" 
                    class="h-8 w-8 border border-gray-300 rounded cursor-pointer" 
                    bind:value={$systemSettings.secondary_color}
                    on:input={() => trackColorChange('secondary_color')}
                    aria-labelledby="secondary-color"
                />
                <input 
                    id="secondary-color"
                    type="text" 
                    class="ml-2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                    bind:value={$systemSettings.secondary_color}
                    on:input={() => trackColorChange('secondary_color')}
                />
            </div>
            <p class="mt-1 text-sm text-gray-500">{t('secondary_color_desc')}</p>

            <!-- Accent Color -->
            <label for="accent-color" class="block text-sm font-medium text-gray-700 mt-4">
                {t('accent_color')} {#if colorChangedFields.has('accent_color')}<span class="text-blue-600 ml-1">({t('modified')})</span>{/if}
            </label>
            <div class="mt-1 flex items-center">
                <input 
                    id="accent-color-picker"
                    type="color" 
                    class="h-8 w-8 border border-gray-300 rounded cursor-pointer" 
                    bind:value={$systemSettings.accent_color}
                    on:input={() => trackColorChange('accent_color')}
                    aria-labelledby="accent-color"
                />
                <input 
                    id="accent-color"
                    type="text" 
                    class="ml-2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                    bind:value={$systemSettings.accent_color}
                    on:input={() => trackColorChange('accent_color')}
                />
            </div>
            <p class="mt-1 text-sm text-gray-500">{t('accent_color_desc')}</p>
        </div>
    </div>

    {#if hasUnsavedColorChanges || logoChanged}
        <div class="bg-amber-50 border-l-4 border-amber-400 p-4 mt-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-amber-700">
                        {t('unsaved_changes')}
                    </p>
                </div>
            </div>
        </div>
    {/if}

    <!-- Hidden form for color settings submission -->
    <form 
        bind:this={settingsForm} 
        action="?/update_system_setting" 
        method="POST" 
        use:enhance={() => {
            return {
                result: ({ form, data }) => {
                    originalValues = { ...$systemSettings };
                    colorChangedFields.clear();
                    hasUnsavedColorChanges = false;
                    toastMessage = "Color settings saved successfully!";
                    toastStatus = true;
                    counter = 2;
                    timeout();
                }
            };
        }}
        class="hidden"
    >
        <input type="hidden" name="settings" value="" />
    </form>
</div>

<div class="p-6 bg-white rounded-lg shadow-md mt-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-xl font-medium text-gray-700">{t('timeout_settings')}</h2>
            <p class="text-sm text-gray-500">{t('timeout_description')}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-3">
        <!-- First Timeout -->
        <div class="flex flex-col justify-between h-32">
          <div>
            <label for="inactive1st" class="block text-sm font-medium text-gray-700 mb-1">
              {t('first_timeout')}
            </label>
            <p class="text-sm text-gray-500">
              {t('first_timeout_desc')}
            </p>
          </div>
          <input
            type="text"
            id="inactive1st"
            class="mt-2 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            bind:value={$systemSettings.inactive_1st}
            disabled
          />
        </div>
      
        <!-- Second Timeout -->
        <div class="flex flex-col justify-between h-32">
          <div>
            <label for="inactive2nd" class="block text-sm font-medium text-gray-700 mb-1">
              {t('second_timeout')}
            </label>
            <p class="text-sm text-gray-500">
              {t('second_timeout_desc')}
            </p>
          </div>
          <input
            type="text"
            id="inactive2nd"
            class="mt-2 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            bind:value={$systemSettings.inactive_2nd}
            disabled
          />
        </div>
    </div>      
</div>

<!-- <div class="p-6 bg-white rounded-lg shadow-md mt-6"> -->
<div class="p-6 bg-white rounded-lg shadow-md mt-6">
    <div>
        <h2 class="text-xl font-medium text-gray-700">{t('csat_settings')}</h2>
        <p class="text-sm text-gray-500">{t('csat_description')}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
        <!-- CSAT Image Upload -->
        <div>
            <div class="flex justify-between items-center mb-2">
                <label class="block text-sm font-medium text-gray-700">
                    {t('upload_csat_image')}
                    {#if csatImageChanged}
                        <span class="text-blue-600 ml-1">
                            ({t('modified')})
                        </span>
                    {/if}
                </label>
                
                <button 
                    type="button" 
                    class="inline-flex items-center px-3 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm 
                        {csatImageChanged ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-400 bg-gray-100 cursor-not-allowed'}"
                    on:click={saveCsatImage}
                    disabled={!csatImageChanged || uploadingCsatImage}
                >
                    {#if uploadingCsatImage}
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('saving')}
                    {:else}
                        {t('save_csat_image')}
                    {/if}
                </button>
            </div>
            
            <div class="mt-1 relative w-full">
                {#if $systemSettings.csat_image}
                    <!-- <div class="relative"> -->
                    <div class="relative flex justify-center items-center">
                        <img 
                            src={$systemSettings.csat_image} 
                            alt="CSAT image preview" 
                            class="object-contain rounded-md shadow max-w-xs max-h-70"
                        />
                        <button 
                            on:click={removeCsatImage}
                            class="absolute top-0 right-0 bg-white rounded-full p-1 shadow-md -mt-2 -mr-2"
                            title="Remove CSAT image"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                {/if}

                <Dropzone
                    id="csat-image-dropzone"
                    class="w-full h-64 border-dashed border-2 border-gray-300 flex flex-col items-center justify-center 
                           { $systemSettings.csat_image ? 'hidden' : '' }"
                    on:drop={handleCsatImageDrop}
                    on:dragover={(event) => event.preventDefault()}
                >
                    <svg aria-hidden="true" class="mb-3 w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p class="mb-2 text-sm text-gray-500">
                        <span class="font-semibold">
                            {t('click_and_drag')}
                        </span> 
                    </p>
                    <p class="text-xs text-gray-500">
                        {t('recommended_size')}: 480px × 480px
                    </p>
                    <input id="csat-image-upload" type="file" accept="image/*" class="hidden" on:change={handleCsatImageUpload} />
                </Dropzone>
            </div>
        </div>

        <!-- CSAT Rating Scale Information -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-md font-medium text-gray-700 mb-3">{t('csat_rating_system')}</h3>
            <p class="text-sm text-gray-600 mb-4">{t('csat_rating_description')}</p>
            
            <div class="space-y-2">
                <div class="flex items-center justify-between p-2 bg-white rounded border">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mr-3">
                        </div>
                        <span class="text-sm font-medium text-gray-700">{t('excellent')}</span>
                    </div>
                    <span class="text-sm font-bold text-green-500">{t('five_points')}</span>
                </div>
                
                <div class="flex items-center justify-between p-2 bg-white rounded border">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-lime-400 flex items-center justify-center mr-3">
                        </div>
                        <span class="text-sm font-medium text-gray-700">{t('good')}</span>
                    </div>
                    <span class="text-sm font-bold text-lime-400">{t('four_points')}</span>
                </div>
                
                <div class="flex items-center justify-between p-2 bg-white rounded border">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-yellow-300 flex items-center justify-center mr-3">
                        </div>
                        <span class="text-sm font-medium text-gray-700">{t('ok')}</span>
                    </div>
                    <span class="text-sm font-bold text-yellow-300">{t('three_points')}</span>
                </div>
                
                <div class="flex items-center justify-between p-2 bg-white rounded border">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-orange-400 flex items-center justify-center mr-3">
                        </div>
                        <span class="text-sm font-medium text-gray-700">{t('poor')}</span>
                    </div>
                    <span class="text-sm font-bold text-orange-400">{t('two_points')}</span>
                </div>
                
                <div class="flex items-center justify-between p-2 bg-white rounded border">
                    <div class="flex items-center">
                        <div class="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center mr-3">
                        </div>
                        <span class="text-sm font-medium text-gray-700">{t('need_improvement')}</span>
                    </div>
                    <span class="text-sm font-bold text-red-500">{t('one_point')}</span>
                </div>
            </div>
            
            <div class="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                <p class="text-xs text-blue-700">
                    <strong>{t('note')}:</strong> {t('csat_calculation_note')}
                </p>
            </div>
        </div>
    </div>
</div>