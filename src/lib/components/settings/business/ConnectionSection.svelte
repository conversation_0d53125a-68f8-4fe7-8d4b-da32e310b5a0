<!-- ConnectionSection.svelte -->
<script lang="ts">
    import { t, language } from '$lib/stores/i18n';
    import { get } from 'svelte/store';

    import { Toast } from 'flowbite-svelte';
    import { fly } from 'svelte/transition';

    import {
        CheckCircleSolid,
        ChevronDownOutline,
        PlusOutline
    } from 'flowbite-svelte-icons';

    import { Input, AccordionItem, Accordion, Dropzone } from 'flowbite-svelte';
    import { Button } from 'flowbite-svelte';

    // import ConnectionFacebook from './ConnectionFacebook.svelte';
    // import ConnectionWhatapps from './ConnectionWhatapps.svelte';
    // import ConnectionLine from './ConnectionLINE.svelte';
    import LineGroup from './LineGroup.svelte';

    export let connectionSettings;
    export let connectors;

    console.log(connectors)
    // Import the new LINE Business connector modal
    import ConnectorLineBusiness from '../../connector/ConnectorLineBusiness.svelte';
    // Import the manage LINE connector modal
    import ManageLineConnector from '../../connector/ManageLineConnector.svelte';

    let toastStatus = false;
    let toastMessage = 'Changes saved successfully!';
    let counter = 0;

    // Add modal state
    let showLineModal = false;
    let showManageModal = false;
    let selectedLineConnection = null;

    // Countdown timer for toast
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }

    // Watch toastStatus and start countdown when it becomes true
    // Not sure when this toast is used
    $: if (toastStatus) {
        counter = 3;
        timeout();
    }

    // Function to handle LINE connection button click
    function handleLineConnect() {
        showLineModal = true;
    }

    // Function to handle manage button click
    function handleManage(connection) {
        selectedLineConnection = connection;
        showManageModal = true;
    }

    // Function to handle connection updated
    function handleConnectionUpdated() {
        // You might want to refresh the connectors data here
        // This could emit an event to parent component or call an API
        console.log('Connection updated, should refresh data');
    }

    // Function to handle connection deleted
    function handleConnectionDeleted() {
        // You might want to refresh the connectors data here
        showManageModal = false;
        console.log('Connection deleted, should refresh data');
    }

    // Check if each connector has connections separately
    $: hasLineConnections = connectors && connectors.line && connectors.line.length > 0;
    $: hasFacebookConnections = connectors && connectors.facebook && connectors.facebook.length > 0;
    $: hasWhatsappConnections = connectors && connectors.whatsapp && connectors.whatsapp.length > 0;
    $: hasAnyConnections = hasLineConnections || hasFacebookConnections || hasWhatsappConnections;

    // States for collapsible sections
    let lineExpanded = false;
    let facebookExpanded = false;
    let whatsappExpanded = false;

    let lang = get(language);
</script>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    {#if toastStatus}
        <Toast
            color="green"
            transition={fly}
            params={{ x: 200 }}
            bind:toastStatus
            class="fixed left-1/2 top-20 -translate-x-1/2 transform"
        >
            <CheckCircleSolid slot="icon" class="h-5 w-5" />
            {toastMessage}
        </Toast>
    {/if}

    <!-- <Accordion flush>
        <ConnectionLine {connectionSettings}/>
        <ConnectionWhatapps />
        <ConnectionFacebook />
    </Accordion> -->

    <!-- Chat Integrations Section -->
    <div class="space-y-6 border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-xl font-semibold text-gray-900">{t('chat_integrations')}</h2>
                <p class="text-sm text-gray-600 mt-1">{t('chat_integrations_description')}</p>
            </div>
        </div>

        <!-- Connected Integrations Section (shown when there are connections) -->
        {#if hasAnyConnections}
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">{t('connected')}</h3>
                
                <!-- Connected LINE integrations -->
                {#if hasLineConnections}
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 cursor-pointer" on:click={() => lineExpanded = !lineExpanded}>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 flex items-center justify-center">
                                <img src="/images/platform-line.png" alt="Line" class="w-6 h-6" />
                            </div>
                            <div>
                                <div class="text-gray-900 font-medium">{t('line_official_account')}</div> 
                                <div class="text-sm text-gray-600">
                                    {#if lang === 'en'}
                                        {t('there_are')} {connectors.line.length} {connectors.line.length > 1 ? 'accounts' : 'account'} {t('accounts_has_been_connected')}
                                    {:else if lang === 'th'}
                                        {t('there_are')} {connectors.line.length} {t('accounts_has_been_connected')}
                                    {/if}
                                </div>


                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <ChevronDownOutline class="w-5 h-5 text-gray-500 transition-transform duration-200 {lineExpanded ? 'rotate-180' : ''}" />
                        </div>
                    </div>
                    {#if lineExpanded}
                        {#each connectors.line as lineConnection}
                            <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200 ml-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 flex items-center justify-center">
                                        <img src="/images/platform-line.png" alt="Line" class="w-6 h-6" />
                                    </div>
                                    <div>
                                        <div class="text-gray-900 font-medium">{lineConnection.name}</div>
                                        <div class="text-sm text-gray-600">Channel ID: {lineConnection.channel_id}</div>
                                        <!-- <div class="text-sm text-gray-600">Provider ID: {lineConnection.line_provider_id}</div> -->
                                        <!-- <div class="text-sm text-gray-600">Provider Name: {lineConnection.line_provider_name}</div> -->
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                                        {t('connected')}
                                    </span>
                                    <button 
                                        class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                        on:click={() => handleManage(lineConnection)}
                                    >
                                        {t('manage')}
                                    </button>
                                </div>
                            </div>
                        {/each}

                        <!-- <LineGroup {connectionSettings} /> -->
                    {/if}
                {/if}

                <!-- Connected Facebook integrations -->
                {#if hasFacebookConnections}
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 cursor-pointer" on:click={() => facebookExpanded = !facebookExpanded}>
                        <div class="flex items-center space-x-3">
                            <div class="relative w-10 h-8">
                                <div class="absolute left-0 top-0 w-8 h-8 flex items-center justify-center z-10">
                                    <img src="/images/platform-facebook.png" alt="Facebook" class="w-6 h-6" />
                                </div>
                                <div class="absolute left-4 top-0 w-8 h-8 flex items-center justify-center z-20">
                                    <img src="/images/platform-instagram.png" alt="Instagram" class="w-6 h-6" />
                                </div>
                            </div>
                            <div>
                                <div class="text-gray-900 font-medium">{t('facebook_instagram')}</div>
                                <div class="text-sm text-gray-600">There are {connectors.facebook.length} account{connectors.facebook.length > 1 ? 's' : ''} has been connected</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <ChevronDownOutline class="w-5 h-5 text-gray-500 transition-transform duration-200 {facebookExpanded ? 'rotate-180' : ''}" />
                        </div>
                    </div>
                    {#if facebookExpanded}
                        {#each connectors.facebook as facebookConnection}
                            <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200 ml-4">
                                <div class="flex items-center space-x-3">
                                    <div class="relative w-10 h-8">
                                        <div class="absolute left-0 top-0 w-8 h-8 flex items-center justify-center z-10">
                                            <img src="/images/platform-facebook.png" alt="Facebook" class="w-6 h-6" />
                                        </div>
                                        <div class="absolute left-4 top-0 w-8 h-8 flex items-center justify-center z-20">
                                            <img src="/images/platform-instagram.png" alt="Instagram" class="w-6 h-6" />
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-gray-900 font-medium">{facebookConnection.name}</div>
                                        <div class="text-sm text-gray-600">Channel ID: {facebookConnection.channel_id}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                                        {t('connected')}
                                    </span>
                                    <button class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                        {t('manage')}
                                    </button>
                                </div>
                            </div>
                        {/each}
                    {/if}
                {/if}

                <!-- Connected WhatsApp integrations -->
                {#if hasWhatsappConnections}
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 cursor-pointer" on:click={() => whatsappExpanded = !whatsappExpanded}>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 flex items-center justify-center">
                                <img src="/images/platform-whatsapp.png" alt="WhatsApp" class="w-6 h-6" />
                            </div>
                            <div>
                                <div class="text-gray-900 font-medium">{t('whatsapp_business')}</div>
                                <div class="text-sm text-gray-600">There are {connectors.whatsapp.length} account{connectors.whatsapp.length > 1 ? 's' : ''} has been connected</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <ChevronDownOutline class="w-5 h-5 text-gray-500 transition-transform duration-200 {whatsappExpanded ? 'rotate-180' : ''}" />
                        </div>
                    </div>
                    {#if whatsappExpanded}
                        {#each connectors.whatsapp as whatsappConnection}
                            <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200 ml-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 flex items-center justify-center">
                                        <img src="/images/platform-whatsapp.png" alt="WhatsApp" class="w-6 h-6" />
                                    </div>
                                    <div>
                                        <div class="text-gray-900 font-medium">{whatsappConnection.name}</div>
                                        <div class="text-sm text-gray-600">Channel ID: {whatsappConnection.channel_id}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                                        {t('connected')}
                                    </span>
                                    <button class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                        {t('manage')}
                                    </button>
                                </div>
                            </div>
                        {/each}
                    {/if}
                {/if}
            </div>
        {/if}

        <!-- Discover Section -->
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">{t('discover')}</h3>
    
            <!-- Line -->
            <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-1">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <img src="/images/platform-line.png" alt="Line" class="w-6 h-6" />
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('line_official_account')}</div>
                        <div class="text-sm text-gray-600">{t('connect_line_description')}</div>
                    </div>
                </div>
                <button 
                    class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors"
                    on:click={handleLineConnect}
                >
                    + {t('connect')}
                </button>
            </div>
    
            <!-- Facebook / Instagram -->
            <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="relative w-10 h-8">
                        <div class="absolute left-0 top-0 w-8 h-8 flex items-center justify-center z-10">
                            <img src="/images/platform-facebook.png" alt="Facebook" class="w-6 h-6" />
                        </div>
                        <div class="absolute left-4 top-0 w-8 h-8 flex items-center justify-center z-20">
                            <img src="/images/platform-instagram.png" alt="Instagram" class="w-6 h-6" />
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('facebook_instagram')}</div>
                        <div class="text-sm text-gray-600">{t('connect_line_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div>
    
            <!-- WhatsApp Business -->
            <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-whatsapp.png" alt="WhatsApp" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('whatsapp_business')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div>

            <!-- Shopee -->
            <!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-shopee.png" alt="Shopee" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('shopee')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
    
            <!-- Lazada -->
            <!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-lazada.png" alt="Lazada" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('lazada')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
    
            <!-- TikTok Shop -->
            <!-- <div class="flex items-center justify-between p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="/images/platform-tiktok.png" alt="TikTok" class="w-6 h-6" />
                    </div>
                    <div>
                        <div class="text-gray-900 font-medium">{t('tiktok_shop')}</div>
                        <div class="text-sm text-gray-600">{t('connect_multiple_description')}</div>
                    </div>
                </div>
                <button class="px-4 py-2 text-black-600 hover:text-black-800 font-medium text-sm border border-black-600 rounded-md hover:bg-gray-100 transition-colors">
                    {t('not_available_yet')}
                </button>
            </div> -->
        </div>
    </div>
    
</div>

<!-- LINE Business Connection Modal -->
<ConnectorLineBusiness bind:showModal={showLineModal} {connectionSettings} />

<!-- LINE Business Manage Modal -->
<ManageLineConnector 
    bind:showModal={showManageModal} 
    bind:lineConnection={selectedLineConnection}
    onConnectionUpdated={handleConnectionUpdated}
    onConnectionDeleted={handleConnectionDeleted}
/>