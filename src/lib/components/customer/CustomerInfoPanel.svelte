<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { Customer } from '$lib/types/customer';
	import InformationTab from './tabs/InformationTab.svelte';
	import RequestSummaryTab from './tabs/RequestSummaryTab.svelte';
	import TimelineTab from './tabs/TimelineTab.svelte';
	import AIGuidanceTab from './tabs/AIGuidanceTab.svelte';

	export let customer: Customer;
	// console.log(customer)
	export let platformId: number | null = null;
	export let access_token: string;

	let activeTab = 'information';
	
	const tabs = [
		{ id: 'information', label: 'Information', key: 'information', component: InformationTab },
		{ id: 'request', label: 'Summary', key: 'summary', component: RequestSummaryTab },
		// { id: 'timeline', label: 'Timeline', key: 'timeline', component: TimelineTab }, //TO DO LATER FOR BVTPA
		{ id: 'ai', label: 'AI Assistant', key: 'ai_assistant', component: AIGuidanceTab },
	];
</script>

<div class="h-full w-full flex flex-col">
	<!-- Tab Navigation -->
	<div class="bg-white border-b border-gray-200 flex-shrink-0">
		<nav class="flex w-full">
			{#each tabs as tab}
				<button
					on:click={() => activeTab = tab.id}
					class="flex-1 px-4 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap text-center
						{activeTab === tab.id 
							? 'border-black text-black bg-white' 
							: 'border-transparent text-gray-500 hover:text-gray-700'}"
				>
					<!-- {tab.label} -->
					{t(tab.key)}
				</button>
			{/each}
		</nav>
	</div>
	
	<!-- Tab Content -->
	<div class="flex-1 overflow-y-auto bg-gray-50 w-full">
		<div class="w-full h-full">
			{#each tabs as tab}
				{#if activeTab === tab.id}
					<svelte:component this={tab.component} {customer} {access_token} {platformId}/>
				{/if}
			{/each}
		</div>
	</div>
</div>