<!-- profiles.svelte -->
<script lang="ts">

	import { t } from '$src/lib/stores/i18n';
    import { page } from '$app/stores';

    export let customer: CustomerInterface;
    export let customer_tags: any;
    import { Indicator } from "flowbite-svelte";
    import { getColorClass } from '$lib/utils';
    import CustomerEdit from '$lib/components/UI/CustomerEdit.svelte';
    import CustomerTag from '$src/lib/components/UI/CustomerTag.svelte';

    $: role = $page.data.role;
    $: isAgent = role === 'Agent';
    console.log({role});
    console.log(role);

    function maskPhoneNumber(phone: string): string {
        if (!phone) return '';
        if (isAgent) {
            const len = phone.length;
            if (len <= 4) return phone;
            return phone.slice(0, 3) + 'x'.repeat(len - 6) + phone.slice(len - 3);
        }
        return phone;
    }


    const hasValidAddress = (address) => {
        if (!address) return false;
        
        const addressFields = [
            'address_line1',
            'address_line2', 
            'city',
            'state_province_region',
            'zip_code',
            'country'
        ];
        
        return addressFields.some(field => 
            address[field] && address[field].toString().trim() !== ''
        );
    };

    // Helper function to format address for display
    const formatAddress = (address) => {
    if (!address) return '';
    
    const parts = [];
    
    if (address.address_line1) parts.push(address.address_line1);
    if (address.address_line2) parts.push(address.address_line2);
    if (address.city) parts.push(address.city);
    if (address.state_province_region) parts.push(address.state_province_region);
    if (address.zip_code) parts.push(address.zip_code);
    if (address.country) parts.push(address.country);
    
    return parts.join(', ');
    };
</script>

<!-- Customer Profile -->
<div class="w-full p-4 mb-4 bg-white rounded-lg border shadow-md"> 
    <!-- Customer Header & Profile image -->
    <div class="text-center">
        <div class="w-20 h-20 bg-gray-100 rounded-full mx-auto mt-3 mb-3 flex items-center justify-center text-2xl font-medium text-gray-600">
            <img
                src={customer.picture_url
                    ? customer.picture_url
                    : '/images/person-logo.png'}
                alt="Profile"
                class="w-full h-full object-cover rounded-full"
            />
        </div>
        <h2 class="text-xl font-semibold">{customer.name || 'Unknown Customer'}</h2>
        <p class="text-sm text-gray-500">{customer.customer_id.toString()}</p>
    </div>

    <div class="mt-4">
        <div class="flex items-center mb-2 justify-between">
            <div class="text-lg font-medium text-gray-700">{t('basic_information')}</div>

            <CustomerEdit {customer} />
        </div>

        <div class="space-y-3">
            <div>
                <label class="text-xs text-gray-500">{t('display_name')}</label>
                <p class="text-sm font-medium">
                    {customer.name ? `${customer.name}`: t('not_provided')}
                </p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('first_name')}</label>
                <p class="text-sm font-medium">
                    {customer.first_name ? `${customer.first_name}`: t('not_provided')}
                </p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('last_name')}</label>
                <p class="text-sm font-medium">
                    {customer.last_name ? `${customer.last_name}`: t('not_provided')}
                </p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('national_id')}</label>
                <p class="text-sm font-medium">{customer.national_id || t('not_provided')}</p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('date_of_birth')}</label>
                <p class="text-sm font-medium">{customer.date_of_birth || t('not_provided')}</p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('address')}</label>
                <p class="text-sm font-medium">
                    {customer.address.address_line1 || t('not_provided')}
                    {#if customer.address.address_line2}
                        <br>{customer.address.address_line2}
                    {/if}
                    {#if customer.address.district || customer.address.province}
                        <br>{[customer.address.district, customer.address.province].filter(Boolean).join(', ')}
                    {/if}
                </p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('phone_number')}</label>
                <p class="text-sm font-medium">{customer.phone || t('not_provided')}</p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('email')}</label>
                <p class="text-sm font-medium">{customer.email || t('not_provided')}</p>
            </div>

            <div>
                <label class="text-xs text-gray-500">{t('contact_channel')}</label>
                <p class="text-sm font-medium">{customer.main_interface_id?.name || 'LINE'}</p>
            </div>        
        </div>
    </div>
</div>

<!-- Customer Tags -->
<div class="w-full p-4 mb-4 bg-white rounded-lg shadow-md border"> 

    <div class="flex items-center mb-2 justify-between">
        <div class="text-lg font-medium text-gray-700">{t('customer_tags')}</div>

        <CustomerTag customer={customer} customer_tags={customer_tags}/>
    </div>

    <!-- <div class="ml-6 text-sm text-blue-500">{customer.tags.map((tag) => tag.name) || ''}</div> -->

    {#each customer.tags as tag}
        <!-- <span class="inline-flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700"> -->
        <span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm"> 
            <!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {tag.color}"></span> -->
            <Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
            {tag.name}
        </span>
    {/each}
</div>