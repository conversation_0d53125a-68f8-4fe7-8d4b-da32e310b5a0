<script lang="ts">
    import { t, language } from '$lib/stores/i18n';
    import { derived, writable } from 'svelte/store';
    import { createEventDispatcher } from 'svelte';

    import {
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Table
    } from 'flowbite-svelte';
    import { Tooltip } from 'flowbite-svelte';
    import {
        EditSolid,
        CaretDownSolid,
        CaretUpSolid
    } from 'flowbite-svelte-icons';
    import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
    import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
    import ChangeTicketPriority from '$src/lib/components/UI/ChangeTicketPriority.svelte';
    import Pagination from '$src/lib/components/UI/pagination.svelte';
    import { 
        displayDate, 
        getStatusClass, 
        getPriorityClass, 
        getSentimentClass, 
        getSentimentIcon 
    } from '$lib/utils';

    // Create event dispatcher
    const dispatch = createEventDispatcher();

    // Props for the component
    export let tickets = [];
    export let users = [];
    export let statuses = [];
    export let priorities = [];
    export let ticket_topics = [];
    export let loginUser = null;
    
    // Pagination state variables
    export let currentPage = 1;
    export let totalPages = 1;
    export let updateCurrentPage: (page: number) => void;

    // Add a prop for ordering and the sort handler
    export let ordering = '';
    export let handleSort = (column: string) => {};

    // Function to handle ticket refresh after actions
    function handleTicketRefresh() {
        dispatch('ticketRefresh');
    }

    // Map frontend column names to backend ordering field names (same as parent)
    const columnFieldMap = {
        'id': 'id',
        'status': 'status_id__id', 
        'priority': 'priority__id',
        'customer': 'customer_id__name',
        'agent': 'owner_id__name', 
        'updated_ago': 'updated_on',  // Frontend displays 'updated_ago' but sorts by 'updated_on'
        'created_on': 'created_on',
        'sentiment': 'latest_sentiment'
    };

    // Helper function to show sort icon - make it reactive
    $: getCaretIcon = (column: string) => {
        const backendField = columnFieldMap[column] || column;
        if (ordering === backendField) {
            return CaretUpSolid; // First state. Ascending for all but time columns
        } else if (ordering === '-' + backendField) {
            return CaretDownSolid; // Second state. Descending for all but time columns
        }
        return null;
    };
</script>

<div class="w-full overflow-x-auto">
    <!-- <Table shadow class="table-fixed min-w-full"> -->
    <Table shadow > 
        <TableHead>
            <TableHeadCell class="w-16 cursor-pointer" on:click={() => handleSort('id')}>
                <div class="flex items-center justify-start">
                    {t('table_no')}
                    {#if getCaretIcon('id')}
                        <svelte:component this={getCaretIcon('id')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>
            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('status')}>
                <div class="flex items-center justify-start">
                    {t('table_status')}
                    {#if getCaretIcon('status')}
                        <svelte:component this={getCaretIcon('status')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('priority')}>
                <div class="flex items-center justify-start">
                    {t('table_priority')}
                    {#if getCaretIcon('priority')}
                        <svelte:component this={getCaretIcon('priority')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-32 text-center cursor-pointer" on:click={() => handleSort('sentiment')}>
                <div class="flex items-center justify-center">
                    {t('table_sentiment')}
                    {#if getCaretIcon('sentiment')}
                        <svelte:component this={getCaretIcon('sentiment')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-40 cursor-pointer" on:click={() => handleSort('customer')}>
                <div class="flex items-center justify-start">
                    {t('table_customer')}
                    {#if getCaretIcon('customer')}
                        <svelte:component this={getCaretIcon('customer')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('agent')}>
                <div class="flex items-center justify-start">
                    {t('table_agent')}
                    {#if getCaretIcon('agent')}
                        <svelte:component this={getCaretIcon('agent')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>
            
            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('created_on')}>
                <div class="flex items-center justify-start">
                    {t('table_time')}
                    {#if getCaretIcon('created_on')}
                        <svelte:component this={getCaretIcon('created_on')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>

            <TableHeadCell class="w-40">{t('table_actions')}</TableHeadCell>

            <TableHeadCell class="w-32 cursor-pointer" on:click={() => handleSort('updated_ago')}>
                <div class="flex items-center justify-start">
                    {t('table_updated_ago')}
                    {#if getCaretIcon('updated_ago')}
                        <svelte:component this={getCaretIcon('updated_ago')} class="inline-block h-4 w-4 ml-1" />
                    {/if}
                </div>
            </TableHeadCell>
        </TableHead>
        <TableBody tableBodyClass="divide-y"> 
            {#if tickets.length === 0}
                <TableBodyRow>
                    <TableBodyCell colspan={9} class="text-center py-4 text-gray-500">
                        {t('table_no_ticket')}
                    </TableBodyCell>
                </TableBodyRow>
            {:else}
                {#each tickets as ticket}
                    <TableBodyRow>
                        <TableBodyCell>
                            <a
                                href="/monitoring/{ticket.id}"
                                class="flex items-center text-blue-600 hover:underline py-2"
                            >
                                {ticket.id}<EditSolid class="h-4 w-4 ml-1" />
                            </a>
                        </TableBodyCell>
                        <TableBodyCell>
                            <div class="flex justify-start">
                                <span class={`${getStatusClass(ticket.status.id)} rounded-md text-sm w-32 text-center`}>
                                    <!-- {ticket.status.name.charAt(0).toUpperCase() + ticket.status.name.slice(1)} -->
                                    {ticket.status.name.charAt(0).toUpperCase() + ticket.status.name.slice(1).split('_').join(' ')}
                                </span>
                            </div>
                        </TableBodyCell>
                        
                        <TableBodyCell>
                            <div class="flex justify-start">  
                                <span class={`${getPriorityClass(ticket.priority.name)} rounded-md text-sm w-24 text-center`}>
                                    {ticket.priority.name ?? "-"}
                                </span>
                            </div>                        
                        </TableBodyCell>

                        <TableBodyCell>
                            <div class="flex justify-center"> 
                                <div class={`flex items-center justify-center gap-1 rounded-md p-2 ${getSentimentClass(ticket.sentiment)}`}>
                                    <img
                                        src={getSentimentIcon(ticket.sentiment)}
                                        alt={ticket.sentiment}
                                        class="w-5 h-5"
                                    />
                                    <Tooltip>{ticket.sentiment ?? t('table_unclassified')}</Tooltip>
                                </div>
                            </div>
                        </TableBodyCell>

                        <TableBodyCell>
                            {#if ticket.customer.name}
                                <div class="text-sm font-medium">{ticket.customer.name}</div>
                                {#if ticket.customer.email}
                                    <div class="text-xs text-gray-500 truncate">{ticket.customer.email}</div>
                                {/if}
                            {:else if ticket.customer.line_user && ticket.customer.line_user.display_name}
                                <div class="text-sm font-medium">{ticket.customer.line_user.display_name}</div>
                                {#if ticket.customer.line_user.user_id}
                                    <div class="text-xs text-gray-500 truncate">ID: {ticket.customer.line_user.user_id}</div>
                                {/if}
                            {:else}
                                <div class="text-sm">{t('table_unknown')}</div>
                            {/if}
                        </TableBodyCell>
                        <TableBodyCell>
                            <div class="text-sm">{ticket.owner.name ? ticket.owner.name : '-'}</div>
                            <div class="text-xs text-gray-500">
                                {ticket.owner.role ? ticket.owner.role : '-'}
                            </div>            
                        </TableBodyCell>
                        <TableBodyCell>
                            <span class="text-sm">{displayDate(ticket.created_on).date} {displayDate(ticket.created_on).time}</span>
                        </TableBodyCell>

                        <!-- TODO - Consistency on mouse over on disabled options -->
                        <TableBodyCell>
                            <div class="action-container">
                                <TransferTicketOwner 
                                    {ticket} 
                                    {users}
                                    loggedInUsername={loginUser.username}
                                    loggedInRole={loginUser.roles[0].name}
                                    onSuccess={handleTicketRefresh}
                                />
                                <ChangeTicketPriority 
                                    {ticket} 
                                    {priorities}
                                    onSuccess={handleTicketRefresh}
                                />
                                <ChangeTicketStatus 
                                    {ticket} 
                                    {statuses} 
                                    {ticket_topics}
                                    onSuccess={handleTicketRefresh}
                                />
                            </div>
                        </TableBodyCell>

                        <TableBodyCell>
                            <div class="text-sm">{ticket.updated_ago}</div>
                        </TableBodyCell>
                    </TableBodyRow>
                {/each}
            {/if}
        </TableBody>
    </Table>
</div>

<!-- Pagination Layout -->	
<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />

<style>
    /* Add custom styles to ensure consistent appearance */
    :global(.tooltip) {
        z-index: 50;
    }
    
    /* Responsive action buttons */
    .action-container {
        display: grid;
        gap: 0.25rem;
    }
    
    /* Large screens - 3 columns */
    @media (min-width: 1024px) {
        .action-container {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    /* Medium screens - 2 columns */
    @media (min-width: 768px) and (max-width: 1023px) {
        .action-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    /* Small screens - 1 column */
    @media (max-width: 767px) {
        .action-container {
            grid-template-columns: 1fr;
        }
    }
    
    /* Ensure table doesn't break layout on small screens */
    @media (max-width: 1024px) {
        :global(table) {
            width: max-content !important;
        }
    }
</style>