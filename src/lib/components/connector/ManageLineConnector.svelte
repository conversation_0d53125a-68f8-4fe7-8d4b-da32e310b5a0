<!-- ManageLineConnector.svelte -->
<script lang="ts">
    import { Button, Modal, Input, Label, Toast, Toggle } from "flowbite-svelte";
    import { CheckCircleSolid, ClipboardSolid, TrashBinSolid, EditOutline } from "flowbite-svelte-icons";
    import { fly } from 'svelte/transition';
    import { t } from '$lib/stores/i18n';
    import { enhance } from '$app/forms';
    // import { ConnectorService } from "$src/lib/api/features/connector/connectors.service";
    import { page } from '$app/stores';

    export let showModal = false;
    export let lineConnection = null;

    export let onConnectionUpdated = () => {};
    export let onConnectionDeleted = () => {};

    let isEditMode = false;
    let isLoading = false;
    
    // Form data - will be populated when modal opens
    let connectionName = "";
    let channelId = "";
    let channelSecret = "";
    let channelAccessToken = "";
    let lineProviderId = "";
    let lineProviderName = "";
    let isEnabled = true;
    let webhookUrl = "";
    
    // Toast state
    let showToast = false;
    let toastMessage = "";
    let toastColor = "green";
    
    // Service instance
    // const connectorService = new ConnectorService();
    
    // Form references
    let updateFormElement: HTMLFormElement;
    let statusFormElement: HTMLFormElement;
    let testFormElement: HTMLFormElement;
    
    // Watch for lineConnection changes to populate form
    $: if (lineConnection && showModal) {
        populateForm();
    }

    function populateForm() {
        if (lineConnection) {
            connectionName = lineConnection.name || "";
            channelId = lineConnection.channel_id || "";
            channelSecret = lineConnection.channel_secret || "";
            channelAccessToken = lineConnection.channel_access_token || "";
            lineProviderId = lineConnection.provider_id || "";
            lineProviderName = lineConnection.provider_name || "";
            webhookUrl = lineConnection.webhook_url || "";
            isEnabled = lineConnection.status !== 'disabled';
        }
    }

    function toggleEditMode() {
        isEditMode = !isEditMode;
        if (!isEditMode) {
            // If canceling edit, restore original values
            populateForm();
        }
    }

    async function handleSave() {
        if (!lineConnection || !updateFormElement) return;
        
        isLoading = true;
        updateFormElement.requestSubmit();
    }

    async function handleStatusToggle() {
        if (!lineConnection || !statusFormElement) return;
        
        isLoading = true;
        statusFormElement.requestSubmit();
    }

    async function handleTest() {
        if (!lineConnection || !testFormElement) return;
        
        isLoading = true;
        testFormElement.requestSubmit();
    }

    function handleClose() {
        showModal = false;
        isEditMode = false;
        // Reset form when closing
        if (lineConnection) {
            populateForm();
        }
    }
    
    function copyWebhookUrl() {
        navigator.clipboard.writeText(webhookUrl);
        showToastMessage(t('webhook_url_copied'), "green");
    }

    function showToastMessage(message: string, color: string = "green") {
        toastMessage = message;
        toastColor = color;
        showToast = true;
        setTimeout(() => {
            showToast = false;
        }, 3000);
    }
</script>

<Modal bind:open={showModal} size="lg" autoclose={false} title={t('manage_line_connection')} class="w-full">
    {#if lineConnection}
        <!-- Update Form -->
        <form 
            bind:this={updateFormElement}
            method="POST"
            action="?/update_line_connector"
            use:enhance={({ formData }) => {
                formData.append('connector_id', lineConnection.id);
                return async ({ result, update }) => {
                    isLoading = false;
                    if (result.type === 'success') {
                        showToastMessage(t('connection_updated_successfully'), "green");
                        isEditMode = false;
                        onConnectionUpdated();
                    } else if (result.type === 'failure') {
                        showToastMessage(result.data?.error || t('update_failed'), "red");
                    }
                    await update();
                };
            }}
            style="display: none;"
        >
            <input type="hidden" name="name" bind:value={connectionName} />
            <input type="hidden" name="channel_id" bind:value={channelId} />
            <input type="hidden" name="channel_secret" bind:value={channelSecret} />
            <input type="hidden" name="channel_access_token" bind:value={channelAccessToken} />
            <input type="hidden" name="line_provider_id" bind:value={lineProviderId} />
        </form>

        <!-- Status Toggle Form -->
        <form 
            bind:this={statusFormElement}
            method="POST"
            action="?/toggle_line_status"
            use:enhance={({ formData }) => {
                formData.append('connector_id', lineConnection.id);
                formData.append('action', isEnabled ? 'enable' : 'disable');
                formData.append('reason', isEnabled ? 'Re-enabled by user' : 'Disabled by user');
                return async ({ result, update }) => {
                    isLoading = false;
                    if (result.type === 'success') {
                        const action = isEnabled ? 'enabled' : 'disabled';
                        showToastMessage(t(`channel_${action}_successfully`), "green");
                        onConnectionUpdated();
                    } else if (result.type === 'failure') {
                        showToastMessage(result.data?.error || t('status_toggle_failed'), "red");
                        isEnabled = !isEnabled; // Revert toggle
                    }
                    await update();
                };
            }}
            style="display: none;"
        >
        </form>

        <!-- Test Connection Form -->
        <form 
            bind:this={testFormElement}
            method="POST"
            action="?/test_line_connector"
            use:enhance={({ formData }) => {
                formData.append('connector_id', lineConnection.id);
                return async ({ result, update }) => {
                    isLoading = false;
                    if (result.type === 'success') {
                        showToastMessage(t('connection_test_successful'), "green");
                    } else if (result.type === 'failure') {
                        showToastMessage(result.data?.error || t('connection_test_failed'), "red");
                    }
                    await update();
                };
            }}
            style="display: none;"
        >
        </form>

        <div class="space-y-6">
            <!-- Status Toggle -->
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">{t('connection_status')}</h3>
                    <p class="text-sm text-gray-600">
                        {isEnabled ? t('connection_active') : t('connection_disabled')}
                    </p>
                </div>
                <Toggle bind:checked={isEnabled} on:change={handleStatusToggle} disabled={isLoading} />
            </div>

            <!-- Connection Details -->
            <div class="space-y-4">
                <!-- Connection Name -->
                <div>
                    <Label for="connection-name" class="block mb-2 text-sm font-medium text-gray-900">
                        {t('connection_name')}
                    </Label>
                    <Input
                        id="connection-name"
                        bind:value={connectionName}
                        placeholder={t('connection_name_placeholder')}
                        disabled={!isEditMode}
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    />
                </div>

                <!-- Channel ID -->
                <div>
                    <Label for="channel-id" class="block mb-2 text-sm font-medium text-gray-900">
                        {t('channel_id')}
                    </Label>
                    <Input
                        id="channel-id"
                        bind:value={channelId}
                        placeholder={t('channel_id_placeholder')}
                        disabled={!isEditMode}
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    />
                </div>

                <!-- Channel Secret -->
                <div>
                    <Label for="channel-secret" class="block mb-2 text-sm font-medium text-gray-900">
                        {t('channel_secret')}
                    </Label>
                    <Input
                        id="channel-secret"
                        bind:value={channelSecret}
                        placeholder={t('channel_secret_placeholder')}
                        disabled={!isEditMode}
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    />
                </div>

                <!-- Channel Access Token -->
                <div>
                    <Label for="channel-access-token" class="block mb-2 text-sm font-medium text-gray-900">
                        {t('channel_access_token')}
                    </Label>
                    <Input
                        id="channel-access-token"
                        bind:value={channelAccessToken}
                        placeholder={t('channel_access_token_placeholder')}
                        disabled={!isEditMode}
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    />
                </div>

                <!-- LINE Provider ID -->
                <div>
                    <Label for="line-provider-id" class="block mb-2 text-sm font-medium text-gray-900">
                        {t('line_provider_id')}
                    </Label>
                    <Input
                        id="line-provider-id"
                        bind:value={lineProviderId}
                        placeholder={t('line_provider_id_description')}
                        disabled={!isEditMode}
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    />
                </div>

                <!-- LINE Provider Name -->
                <div>
                    <Label for="line-provider-name" class="block mb-2 text-sm font-medium text-gray-900">
                        {t('line_provider_name')}
                    </Label>
                    <Input
                        id="line-provider-name"
                        bind:value={lineProviderName}
                        placeholder={t('line_provider_name_description')}
                        disabled={!isEditMode}
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    />
                </div>

                <!-- Webhook URL -->
                <div>
                    <Label class="block mb-2 text-sm font-medium text-gray-900">
                        {t('webhook_url')}
                    </Label>
                    <div class="flex gap-2">
                        <Input
                            value={webhookUrl}
                            disabled={true}
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        />
                        <Button 
                            type="button"
                            color="alternative" 
                            size="sm" 
                            on:click={copyWebhookUrl}
                            class="px-3 py-2"
                        >
                            <ClipboardSolid class="w-4 h-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    {/if}
    
    <svelte:fragment slot="footer">
        <div class="flex justify-between w-full">
            <div class="flex gap-2">
                <Button 
                    type="button" 
                    color="blue" 
                    size="sm"
                    on:click={handleTest}
                    disabled={isLoading}
                >
                    {isLoading ? t('testing') : t('test_connection')}
                </Button>
            </div>
            
            <div class="flex gap-2">
                {#if isEditMode}
                    <Button type="button" color="alternative" on:click={toggleEditMode}>
                        {t('cancel')}
                    </Button>
                    <Button 
                        type="button" 
                        color="dark" 
                        on:click={handleSave}
                        disabled={isLoading}
                    >
                        {isLoading ? t('saving') : t('save_changes')}
                    </Button>
                {:else}
                    <Button type="button" color="dark" on:click={toggleEditMode}>
                        <EditOutline class="w-4 h-4 mr-2" />
                        {t('edit')}
                    </Button>
                {/if}
            </div>
        </div>
    </svelte:fragment>
</Modal>

<!-- Toast Messages -->
{#if showToast}
    <Toast
        color={toastColor}
        transition={fly}
        params={{ x: 200 }}
        bind:toastStatus={showToast}
        class="fixed left-1/2 top-20 -translate-x-1/2 transform z-50"
    >
        <CheckCircleSolid slot="icon" class="h-5 w-5" />
        {toastMessage}
    </Toast>
{/if}