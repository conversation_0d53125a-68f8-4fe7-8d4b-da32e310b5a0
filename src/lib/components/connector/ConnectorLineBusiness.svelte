<!-- ConnectionLineBusiness.svelte -->
<script lang="ts">
    import { Button, Modal, Input, Label, Radio, Toast, Dropzone } from "flowbite-svelte";
    import { CheckCircleSolid, ClipboardSolid, ChevronLeftOutline, ChevronRightOutline } from "flowbite-svelte-icons";
    import { fly } from 'svelte/transition';
    import { t } from '$lib/stores/i18n';
    import { enhance } from '$app/forms';

    export let showModal = false;
    export let connectionSettings;

    // Connection Details (Required)
    let connectionName = "";
    let channelId = "";
    let channelSecret = "";
    let channelAccessToken = "";
    let lineProviderId = "";
    let lineProviderName = "";
    let isVerified = false;

    // Toast state
    let showToast = false;
    let toastMessage = "";
    let toastStatus = false;
    
    // Form reference
    let formElement: HTMLFormElement;
    
    // Webhook URL (this would typically come from your backend)
    // let webhookUrl = "https://api.yourapp.com/webhook/line";

    async function handleConnect() {
        // Validate required fields
        if (!formValid) {
            toastMessage = t('please_fill_required_fields');
            showToast = true;
            setTimeout(() => {
                showToast = false;
            }, 3000);
            return;
        }
        
        try {
            // Save the connection data
            const formData = new FormData();
            formData.append('name', connectionName);
            formData.append('channel_id', channelId);
            formData.append('channel_secret', channelSecret);
            formData.append('channel_access_token', channelAccessToken);
            formData.append('line_provider_id', lineProviderId);
            formData.append('line_provider_name', lineProviderName);
            
            const response = await fetch('?/create_line_connector', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                toastMessage = t('connection_saved_successfully');
                showToast = true;
                setTimeout(() => {
                    showToast = false;
                }, 3000);
                showModal = false;
                resetForm();
            } else {
                toastMessage = t('connection_failed');
                showToast = true;
                setTimeout(() => {
                    showToast = false;
                }, 3000);
            }
        } catch (error) {
            toastMessage = t('connection_failed');
            showToast = true;
            setTimeout(() => {
                showToast = false;
            }, 3000);
        }
    }

    function handleCancel() {
        showModal = false;
        resetForm();
    }

    function resetForm() {
        connectionName = "";
        channelId = "";
        channelSecret = "";
        channelAccessToken = "";
        lineProviderId = "";
        lineProviderName = "";
        isVerified = false;
    }
    
    // function copyWebhookUrl() {
    //     navigator.clipboard.writeText(webhookUrl);
    //     toastMessage = t('webhook_url_copied');
    //     showToast = true;
    //     setTimeout(() => {
    //         showToast = false;
    //     }, 2000);
    // }

    // Validation for the form
    $: formValid = connectionName.trim() !== "" && 
                   channelId.trim() !== "" && 
                   channelSecret.trim() !== "" && 
                   channelAccessToken.trim() !== "" && 
                   lineProviderId.trim() !== "" && 
                   lineProviderName.trim() !== "";
</script>

<Modal bind:open={showModal} size="lg" autoclose={false} title={t('connect_line_business')} class="w-full">
    <!-- Header -->
    <div slot="header" class="flex items-center justify-between w-full">
        <h3 class="text-xl font-semibold text-gray-900">
            {t('connect_line_business')}
        </h3>
    </div>
    
    <form 
        bind:this={formElement}
        method="POST"
        action="?/create_line_connector"
        novalidate
        use:enhance={({ formData, submitter }) => {
            return async ({ result, update }) => {
                if (result.type === 'success') {
                    toastMessage = t('connection_saved_successfully');
                    showToast = true;
                    
                    // Auto hide toast after 3 seconds
                    setTimeout(() => {
                        showToast = false;
                    }, 3000);
                    
                    // Close modal and reset form
                    showModal = false;
                    resetForm();
                } else if (result.type === 'failure') {
                    toastMessage = result.data?.error || t('connection_failed');
                    showToast = true;
                    setTimeout(() => {
                        showToast = false;
                    }, 3000);
                }
                await update();
            };
        }}
    >
        <div class="space-y-4">
            <!-- Connection Name -->
            <div>
                <Label for="connection-name" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('connection_name')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="connection-name"
                    name="name"
                    bind:value={connectionName}
                    placeholder={t('connection_name_placeholder')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('connection_name_description')}</p> -->
            </div>

            <!-- Channel ID -->
            <div>
                <Label for="channel-id" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('channel_id')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="channel-id"
                    name="channel_id"
                    bind:value={channelId}
                    placeholder={t('channel_id_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('channel_id_description')}</p> -->
            </div>

            <!-- Channel Secret -->
            <div>
                <Label for="channel-secret" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('channel_secret')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="channel-secret"
                    name="channel_secret"
                    bind:value={channelSecret}
                    placeholder={t('channel_secret_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('channel_secret_description')}</p> -->
            </div>

            <!-- Channel Access Token -->
            <div>
                <Label for="channel-access-token" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('channel_access_token')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="channel-access-token"
                    name="channel_access_token"
                    bind:value={channelAccessToken}
                    placeholder={t('channel_access_token_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('channel_access_token_description')}</p> -->
            </div>

            <!-- LINE Provider ID -->
            <div>
                <Label for="line-provider-id" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('line_provider_id')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="line-provider-id"
                    name="line_provider_id"
                    bind:value={lineProviderId}
                    placeholder={t('line_provider_id_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('line_provider_id_description')}</p> -->
            </div>

            <!-- LINE Provider Name -->
            <div>
                <Label for="line-provider-name" class="block mb-2 text-sm font-medium text-gray-900">
                    {t('line_provider_name')} <span class="text-red-500">*</span>
                </Label>
                <Input
                    id="line-provider-name"
                    name="line_provider_name"
                    bind:value={lineProviderName}
                    placeholder={t('line_provider_name_description')}
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    required
                />
                <!-- <p class="mt-1 text-sm text-gray-500">{t('line_provider_name_description')}</p> -->
            </div>

            <!-- Webhook Settings - Show only when Channel ID is filled -->
            <div>
                <div class="flex items-center gap-2">
                    <p class="text-sm text-gray-500">{t('webhook_settings_description')}</p>
                    <!-- {#if channelId}
                        <Button 
                            type="button"
                            color="alternative" 
                            size="sm" 
                            on:click={copyWebhookUrl}
                            class="px-2 py-1 text-sm"
                        >
                            <ClipboardSolid class="w-4 h-4 mr-1" />
                            {t('copy')}
                        </Button>
                    {/if} -->
                </div>
            </div>
        </div>

        <!-- Verified Radio Button -->
        <!-- <div>
            <Label class="block mb-2 text-sm font-medium text-gray-900">
                {t('verification_status')}
            </Label>
            <div class="flex items-center space-x-4">
                <Radio bind:group={isVerified} value={false} class="text-blue-600 focus:ring-blue-500">
                    {t('not_verified')}
                </Radio>
                <Radio bind:group={isVerified} value={true} class="text-blue-600 focus:ring-blue-500">
                    {t('verified')}
                </Radio>
            </div>
            <p class="mt-1 text-sm text-gray-500">{t('verification_status_description')}</p>
        </div> -->
    </form>
    
    <svelte:fragment slot="footer">
        <div class="flex justify-between w-full">
            <div>
                <Button type="button" color="alternative" on:click={handleCancel} class="rounded-lg border border-gray-300 text-gray-700 bg-white hover:bg-gray-50">
                    {t('cancel')}
                </Button>
            </div>
            
            <div>
                <Button 
                    type="button" 
                    color="primary" 
                    on:click={handleConnect}
                    disabled={!formValid}
                    class="rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                    {t('save')}
                </Button>
            </div>
        </div>
    </svelte:fragment>
</Modal>

<!-- Success Toast -->
{#if showToast}
    <Toast
        color="green"
        transition={fly}
        params={{ x: 200 }}
        bind:toastStatus={showToast}
        class="fixed left-1/2 top-20 -translate-x-1/2 transform z-50"
    >
        <CheckCircleSolid slot="icon" class="h-5 w-5" />
        {toastMessage}
    </Toast>
{/if}