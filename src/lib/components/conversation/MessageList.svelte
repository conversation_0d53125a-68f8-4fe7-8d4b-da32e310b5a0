<script lang="ts">
	import { onMount, afterUpdate, createEventDispatcher } from 'svelte';
	import { t, language } from '$lib/stores/i18n';

	import MessageItem from './MessageItem.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';
	import InfiniteScroll from '../common/InfiniteScroll.svelte';

	import type { Message } from '$lib/types/customer';
	import { formatMessageDate } from '$lib/utils/messageFormatter';

	import { Badge } from 'flowbite-svelte';
	import { TicketSolid } from 'flowbite-svelte-icons';

	export let messages: Message[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();

	let scrollContainer: HTMLElement;
	let shouldScrollToBottom = true;
	let isNearBottom = true;

	// Sticky header state
	let stickyDate: string = '';
	let showStickyHeader = false;
	let messageGroupElementsByTicket: HTMLElement[] = [];
	let dateGroupElements: HTMLElement[] = [];

	onMount(() => {
		scrollToBottom();
	});

	afterUpdate(() => {
		if (shouldScrollToBottom && isNearBottom) {
			scrollToBottom();
		}
	});

	function scrollToBottom() {
		if (scrollContainer) {
			scrollContainer.scrollTop = scrollContainer.scrollHeight;
		}
	}

	function handleScroll() {
		if (!scrollContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Check if user is near bottom (within 100px)
		isNearBottom = distanceFromBottom < 100;

		// Check if scrolled to top for loading more
		if (scrollTop === 0 && messages.length > 0) {
			dispatch('loadMore');
		}

		// Update sticky header based on scroll position
		updateStickyHeader();
	}

	function updateStickyHeader() {
		if (!scrollContainer || dateGroupElements.length === 0) return;

		const scrollTop = scrollContainer.scrollTop;
		const containerTop = scrollContainer.getBoundingClientRect().top;

		// Find the topmost visible date group
		let currentDateGroup = null;

		for (let i = 0; i < dateGroupElements.length; i++) {
			const element = dateGroupElements[i];
			if (!element) continue;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top - containerTop;

			// If this date group is visible or partially visible at the top
			if (elementTop <= 20) {
				// 20px threshold for sticky header activation
				currentDateGroup = getAllDateGroups()[i];
			} else {
				break;
			}
		}

		if (currentDateGroup && scrollTop > 50) {
			// Only show sticky header after scrolling 50px
			showStickyHeader = true;
			stickyDate = formatMessageDate(currentDateGroup.messages[0].created_on);
		} else {
			showStickyHeader = false;
		}
	}

	// Helper function to get all date groups in a flat array for tracking
	function getAllDateGroups() {
		const allDateGroups: { messages: Message[] }[] = [];
		messageGroupedByTicketAndDate.forEach((ticketGroup) => {
			ticketGroup.dateGroups.forEach((dateGroup) => {
				allDateGroups.push(dateGroup);
			});
		});
		return allDateGroups;
	}

	// Helper function to calculate global date group index
	function getGlobalDateGroupIndex(ticketGroupIndex: number, dateGroupIndex: number): number {
		let globalIndex = 0;
		for (let i = 0; i < ticketGroupIndex; i++) {
			globalIndex += messageGroupedByTicketAndDate[i].dateGroups.length;
		}
		return globalIndex + dateGroupIndex;
	}

	// Group messages by ticket, then by date within each ticket
	function groupMessagesByTicketAndDate(messages: Message[]) {
		const groups: {
			ticketId: number;
			dateGroups: { date: string; messages: Message[] }[];
		}[] = [];
		let currentTicketId = -1;

		messages.forEach((msg) => {
			const msgDate = new Date(msg.created_on).toLocaleDateString();

			if (msg.ticket_id !== currentTicketId) {
				// New ticket group
				currentTicketId = msg.ticket_id;
				groups.push({
					ticketId: currentTicketId,
					dateGroups: [
						{
							date: msgDate,
							messages: [msg]
						}
					]
				});
				console.log('New ticket group created:', groups[groups.length - 1]);
			} else {
				// Same ticket, check if we need a new date group
				const currentTicketGroup = groups[groups.length - 1];
				const lastDateGroup =
					currentTicketGroup.dateGroups[currentTicketGroup.dateGroups.length - 1];

				if (lastDateGroup.date !== msgDate) {
					// New date within the same ticket
					currentTicketGroup.dateGroups.push({
						date: msgDate,
						messages: [msg]
					});
				} else {
					// Same date, add to existing date group
					lastDateGroup.messages.push(msg);
				}
			}
		});
		return groups;
	}

	// Check if should show avatar (first message or different sender)
	function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
		if (index === 0) return true;
		const prevMessage = messages[index - 1];
		return prevMessage.is_self !== message.is_self || prevMessage.user_name !== message.user_name;
	}

	$: messageGroupedByTicketAndDate = groupMessagesByTicketAndDate(messages);

	// Calculate total number of date groups across all tickets
	$: totalDateGroups = messageGroupedByTicketAndDate.reduce((total, ticketGroup) => {
		return total + ticketGroup.dateGroups.length;
	}, 0);

	// Ensure messageGroupElementsByTicket array is properly sized for the new nested structure
	$: if (messageGroupedByTicketAndDate.length !== messageGroupElementsByTicket.length) {
		messageGroupElementsByTicket = new Array(messageGroupedByTicketAndDate.length);
	}

	// Ensure dateGroupElements array is properly sized
	$: if (totalDateGroups !== dateGroupElements.length) {
		dateGroupElements = new Array(totalDateGroups);
	}
</script>

<div
	bind:this={scrollContainer}
	on:scroll={handleScroll}
	class="custom-scrollbar relative flex-1 overflow-y-auto bg-gray-50 px-6 py-4"
>
	<!-- Sticky Header -->
	{#if showStickyHeader}
		<div class="sticky top-0 z-10 mb-4 pb-2 transition-all duration-200">
			<div class="my-4 flex items-center justify-center">
				<span
					class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
				>
					{stickyDate}
				</span>
			</div>
		</div>
	{/if}
	{#if loading && messages.length === 0}
		<div class="flex h-full items-center justify-center">
			<LoadingSpinner />
		</div>
	{:else}
		<!-- Load more indicator at top -->
		{#if loading && messages.length > 0}
			<div class="flex justify-center py-2">
				<LoadingSpinner size="sm" />
			</div>
		{/if}

		<!-- Messages grouped by ticket and date -->
		{#each messageGroupedByTicketAndDate as ticketGroup, ticketGroupIndex}
			<div bind:this={messageGroupElementsByTicket[ticketGroupIndex]} class="message-group">
				<!-- Ticket separator -->
				<div class="my-10 my-4 flex items-center justify-center">
					<span
						class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
					>
						<TicketSolid class="mr-2 h-5 w-5" />
						{ticketGroup.ticketId}
					</span>
				</div>

				<!-- Date groups within ticket -->
				{#each ticketGroup.dateGroups as dateGroup, dateGroupIndex}
					{@const globalDateGroupIndex = getGlobalDateGroupIndex(ticketGroupIndex, dateGroupIndex)}
					<!-- Date separator -->
					<div
						bind:this={dateGroupElements[globalDateGroupIndex]}
						class="my-4 flex items-center justify-center"
					>
						<span
							class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
						>
							{formatMessageDate(dateGroup.messages[0].created_on)}
						</span>
					</div>

					<!-- Messages in date group -->
					{#each dateGroup.messages as message, messageIndex}
						<MessageItem
							{message}
							showAvatar={shouldShowAvatar(message, messageIndex, dateGroup.messages)}
						/>
					{/each}
				{/each}
			</div>
		{/each}

		{#if messages.length === 0}
			<div class="mt-8 text-center text-gray-500">
				{t('no_messages')}
			</div>
		{/if}
	{/if}
</div>

<style>
	/* Smooth scroll behavior */
	.custom-scrollbar {
		scroll-behavior: smooth;
	}

	/* Message group styling for better tracking */
	.message-group {
		position: relative;
	}
</style>
