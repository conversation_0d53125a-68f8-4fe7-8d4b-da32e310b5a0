<!-- CustomerTag.svelte -->
<script lang="ts">

    import { t } from '$src/lib/stores/i18n';
    import { Indicator } from 'flowbite-svelte';
    import { enhance } from '$app/forms';
    import { Button, Modal, Alert, Checkbox } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
    import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

    // Expecting the current customer and the list of customer_tags as props.
    export let customer: any;
    export let customer_tags: any[];
    console.log('CustomerTag component initialized with customer:', customer);

    let customerAssignTagForm: HTMLFormElement;
    let customerAssignTagModalOpen = false;
    let currentcustomer: any = null;
    let selectedTagIds: (string | number)[] = [];
    
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    // Open the modal and initialize with the current customer's tags if available.
    function opencustomerAssignTagModal(customer: any) {
        currentcustomer = { ...customer };

        if (currentcustomer.tags && Array.isArray(currentcustomer.tags)) {
            selectedTagIds = currentcustomer.tags
                .map(tag => typeof tag === 'object' ? tag.id : tag)
                .filter(id => id !== undefined && id !== null && !isNaN(Number(id)));
        } else {
            selectedTagIds = [];
        }

        customerAssignTagModalOpen = true;
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    function handlecustomerAssigncustomer_tagsubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: customerAssignTagModalOpen,
        setModalOpen: (value: boolean) => customerAssignTagModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };

    $: tagOptions = customer_tags?.map(tag => ({
        value: tag.id,
        name: `${tag.name}`,
        color: `${tag.color}`
    })) || [];

    // Debug logging
    // $: {
    //     console.log('CustomerTag component debug:');
    //     console.log('- customer_tags prop:', customer_tags);
    //     console.log('- tagOptions computed:', tagOptions);
    //     console.log('- tagOptions length:', tagOptions.length);
    // }
</script>

<!-- Button to open the assign tag modal -->
<Button class="text-gray-700 bg-white border hover:bg-gray-100 flex items-center gap-2 shadow-md" on:click={() => opencustomerAssignTagModal(customer)}>
    {t('edit_tag')}
</Button>

<!-- Modal for assigning customer_tags -->
<Modal bind:open={customerAssignTagModalOpen} size="md" autoclose={true} class="w-full">
    <h2 slot="header" class="text-sm">{t('assign_customer_tags')}</h2>
    {#if currentcustomer}
        {#if showSuccessMessage}
            <Alert color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        <form 
            bind:this={customerAssignTagForm} 
            action="?/assign_customer_tag" 
            method="POST" 
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handlecustomerAssigncustomer_tagsubmit}
        >
            <input type="hidden" name="customer_id" value={customer.customer_id}>
            <div class="min-h-[200px]">
                <label for="Selectedcustomer_tags" class="block text-sm font-medium text-gray-700 mb-1 text-left">
                    {t('select_customer_tags')}
                </label>
                {#if tagOptions.length > 0}
                    {#each tagOptions as tag (tag.value)}
                        <div class="mb-2">
                            <Checkbox 
                                checked={selectedTagIds.includes(tag.value)} 
                                value={tag.value}
                                on:change={() => {
                                    if (selectedTagIds.includes(tag.value)) {
                                        selectedTagIds = selectedTagIds.filter(id => id !== tag.value);
                                    } else {
                                        selectedTagIds = [...selectedTagIds, tag.value];
                                    }
                                }}
                            >
                                <Indicator size="lg" class={`mr-1 ${getColorClass(tag.color)}`} />
                                {tag.name}
                            </Checkbox>
                        </div>
                    {/each}
                {:else}
                    <div class="text-gray-500 text-sm py-4">
                        {t('no_tags_available')}
                    </div>
                {/if}
                <input type="hidden" name="tag_ids[]" value={selectedTagIds}>
            </div>
        </form>
    {/if}
    <svelte:fragment slot="footer">
        <Button color="blue" on:click={() => customerAssignTagForm.requestSubmit()}>{t('confirm')}</Button>
        <Button color="none" on:click={() => customerAssignTagModalOpen = false}>{t('cancel')}</Button>
    </svelte:fragment>
</Modal>