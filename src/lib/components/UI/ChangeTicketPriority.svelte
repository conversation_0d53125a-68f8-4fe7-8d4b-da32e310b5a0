<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Select, Label, Textarea, Radio } from 'flowbite-svelte';
	import { InfoCircleSolid } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	export let ticket: any;
	export let priorities: any[] = [];
	export let isDropDownItem: boolean = false;
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	let ticketTransferOwnerForm: HTMLFormElement;
	let ticketTransferOwnerModalOpen = false;
	let transferingTicketOwner: any = null;
	let selectedUserId: string | number = '';
	let initialPriorityId: number = 0;

	// State variables for handling messages - REMOVED, using toast instead
	// let showSuccessMessage = false;
	// let showErrorMessage = false;
	// let successMessage = '';
	// let errorMessage = '';

	function openTicketTransferOwnerModal(ticket: any) {
		transferingTicketOwner = { ...ticket };
		initialPriorityId = ticket.priority ? ticket.priority.id : 0;
		selectedUserId = ticket.priority ? ticket.priority.id : '';
		ticketTransferOwnerModalOpen = true;
		// Reset messages when opening modal - REMOVED, using toast instead
		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';
	}

	function handleTicketTransferOwnerSubmit(event: Event) {
		// Reset messages - REMOVED, using toast instead
		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';
	}

	// TODO - Delete this if the new one is work
	// $: enhanceOptions = {
	//     modalOpen: ticketTransferOwnerModalOpen,
	//     setModalOpen: (value: boolean) => ticketTransferOwnerModalOpen = value,
	//     setSuccessMessage: (value: boolean) => showSuccessMessage = value,
	//     setErrorMessage: (value: boolean) => showErrorMessage = value,
	//     setErrorText: (value: string) => errorMessage = value
	// };

	$: enhanceOptions = {
		modalOpen: ticketTransferOwnerModalOpen,
		setModalOpen: (value: boolean) => (ticketTransferOwnerModalOpen = value),
		useToastOnSuccess: true,
		useToastOnError: true,
		closeModalOnSuccess: true,
		onSuccess: onSuccess // Add the success callback
	};

	// Function to get priority color classes for the border/background
	function getPriorityColorClasses(priorityName: string) {
		switch (priorityName.toLowerCase()) {
			case 'low':
				return 'border-gray-200 bg-gray-100 text-gray-700';
			case 'medium':
				return 'border-yellow-200 bg-yellow-200 text-yellow-700';
			case 'high':
				return 'border-orange-200 bg-orange-200 text-orange-700';
			case 'immediately':
				return 'border-red-200 bg-red-200 text-red-700';
			default:
				return 'border-gray-200 bg-gray-100 text-gray-700';
		}
	}

	// Function to get radio button color based on priority name
	function getRadioColor(priorityName: string) {
		switch (priorityName.toLowerCase()) {
			case 'low':
				return 'gray';
			case 'medium':
				return 'yellow';
			case 'high':
				return 'orange';
			case 'immediately':
				return 'red';
			default:
				return 'gray';
		}
	}
</script>

{#if isDropDownItem}
	<Button
		color="none"
		class="w-full justify-start text-left hover:bg-gray-100"
		on:click={() => openTicketTransferOwnerModal(ticket)}
	>
		{t('priority_modal_title')}
	</Button>
{:else}
	<Button
		size="xs"
		class="bg-black text-white"
		color="black"
		on:click={() => openTicketTransferOwnerModal(ticket)}
	>
		<!-- Transfer Ownership -->
		{t('priority_modal_button')}
	</Button>
{/if}

<Modal bind:open={ticketTransferOwnerModalOpen} size="md" autoclose={false} class="w-full">
	<!-- Header -->
	<h2 slot="header" class="inline-flex items-center">
		<InfoCircleSolid class="me-2.5 h-5 w-5" />{t('priority_modal_title')}
	</h2>

	{#if transferingTicketOwner}

		<form
			bind:this={ticketTransferOwnerForm}
			action="?/ticket_priority_change"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleTicketTransferOwnerSubmit}
		>
			<input type="hidden" name="ticket_id" value={transferingTicketOwner.id} />
			<div>
				<!-- Priority Selection with Radio Buttons -->
				<label class="mb-3 block text-sm font-medium text-gray-700">
					{t('priority_select_label')}
				</label>
				<div class="grid grid-cols-1 gap-3 md:grid-cols-2">
					{#each priorities as priority}
						{@const isCurrentPriority = priority.id === initialPriorityId}
						<div class={`rounded-md border ${getPriorityColorClasses(priority.name)} shadow-md ${isCurrentPriority ? 'opacity-50' : ''}`}>
							<Radio 
								name="ticketPriority" 
								value={priority.id} 
								bind:group={selectedUserId} 
								color={getRadioColor(priority.name)}
								class="w-full p-4"
								disabled={isCurrentPriority}
							>
								{priority.name}
								{#if isCurrentPriority}
									<span class="ml-1">({t('current')})</span>
								{/if}
							</Radio>
						</div>
					{/each}
				</div>

				<!-- Note Textarea -->
				<!-- <div class="mb-6">
                    <Label for="note" class="block mb-2">Note</Label>
                    <Textarea id="note" placeholder="optional..." rows="2" name="note" />
                </div> -->
				<input type="hidden" name="new_priority_id" value={selectedUserId} />
			</div>
		</form>
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<Button color="dark" on:click={() => ticketTransferOwnerForm.requestSubmit()}>{t('confirm')}</Button>
		<Button color="none" on:click={() => (ticketTransferOwnerModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>