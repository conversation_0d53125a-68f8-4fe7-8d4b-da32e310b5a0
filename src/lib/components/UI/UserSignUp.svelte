<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { toastStore } from '$lib/stores/toastStore';
	import { parseErrorMessages } from '$lib/utils/errorParser';

	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Alert, Select } from 'flowbite-svelte';
	import {
		CloseOutline,
		CheckOutline,
		ChevronSortOutline,
		EyeSolid,
		EyeSlashSolid
	} from 'flowbite-svelte-icons';

	let signUpForm: HTMLFormElement;
	let signUpModalOpen = false;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {};

	let passedStep1 = false;
	let passedStep2 = false;
	let passedStep3 = false;
	let passedStep4 = false;

	// Flag to track if form should be reset when modal closes
	let shouldResetOnClose = true;

	// Tab navigation state
	let activeTab = 0;

	const languageOptions = [
		{ value: 'en', name: t('language_name_en') },
		{ value: 'th', name: t('language_name_th') }
	];

	// Function to dismiss all error alerts when user starts typing
	// function dismissAlerts() {
	// 	showErrorMessage = false;
	// 	errorMessage = '';
	// 	fieldErrors = {};
	// }

	// Validation errors for contact fields (separate from server errors)
	let validationErrors = {
		personal_phone: '',
		personal_email: '',
		work_phone: '',
		work_email: ''
	};

	function validatePhoneNumber(phone: string) {
		if (!phone.trim()) return { isValid: true, error: '' }; // Optional field
		if (phone.length > 20)
			return { isValid: false, error: 'Phone number must be 20 characters or less' };
		return { isValid: true, error: '' };
	}

	// Email validation function
	function validateEmail(email: string) {
		if (!email.trim()) return { isValid: true, error: '' }; // Optional field
		const emailRegex = /^[a-z0-9](?:[a-z0-9_\-\.]*[a-z0-9])?@[^\s@]+\.[^\s@]+$/i;
		// Explanation:
		// ^[a-z0-9]                  	- starts with a-z or 0-9
		// (?:[a-z0-9_\-\.]*[a-z0-9])? 	- optional: followed by any a-z, 0-9, _, -, . and ends with a-z or 0-9
		// @[^\s@]+                 	- @ and domain part (no @ or space)
		// \.[^\s@]+$               	- . and TLD (no @ or space)
		if (!emailRegex.test(email)) {
			return { isValid: false, error: t('signup_error_invalid_email') };
		}
		return { isValid: true, error: '' };
	}

	// Name input handler with filtering and validation
	function handleNameInput(
		event: Event,
		fieldName: 'name' | 'username' | 'first_name' | 'last_name'
	) {
		const target = event.target as HTMLInputElement;

		let filteredValue = '';
		// Allow last_name to contain whitespace
		if (fieldName === 'last_name') {
			filteredValue = target.value.replace(/[^a-zA-Z\s]/g, '');
			// Explanation:
			// [^a-zA-Z] 	- any character that is not a-z or A-Z
			// /g 			- global flag, to replace all occurrences
		} else if (fieldName === 'username') {
			// For username: Allow letters, numbers, and common special characters
			filteredValue = target.value.replace(/[^a-zA-Z0-9._-]/g, '');
			// Regex explanation:
			// [^a-zA-Z0-9._-] - Match any character that is NOT:
			//   a-z           - lowercase letters
			//   A-Z           - uppercase letters
			//   0-9           - digits
			//   .             - period/dot
			//   _             - underscore
			//   -             - hyphen/dash
			// /g              - Global flag to replace ALL occurrences
		} else {
			filteredValue = target.value.replace(/[^a-zA-Z]/g, '');
			// Explanation:
			// [^a-zA-Z\s] 	- any character that is not a-z, A-Z, or whitespace
			// /g 			- global flag, to replace all occurrences
		}

		// Update form data with filtered value
		formData[fieldName] = filteredValue;

		fieldErrors[fieldName] = [];
		delete fieldErrors[fieldName];

		// Also dismiss server alerts
		// dismissAlerts();
	}

	// Phone input handler with filtering and validation
	function handlePhoneInput(event: Event, fieldName: 'personal_phone' | 'work_phone') {
		const target = event.target as HTMLInputElement;
		const filteredValue = target.value.replace(/[^0-9+]/g, '');

		// Update form data with filtered value
		formData[fieldName] = filteredValue;

		// Validate and update error state
		const validation = validatePhoneNumber(filteredValue);
		validationErrors[fieldName] = validation.error;

		// Also dismiss server alerts
		// dismissAlerts();
	}

	// Email input handler with validation
	function handleEmailInput(event: Event, fieldName: 'personal_email' | 'work_email') {
		const target = event.target as HTMLInputElement;

		// Validate and update error state
		const validation = validateEmail(target.value);
		validationErrors[fieldName] = validation.error;

		fieldErrors[fieldName] = [];
		delete fieldErrors[fieldName];

		// Also dismiss server alerts
		// dismissAlerts();
	}

	// Function to reset form to initial state
	function resetFormData() {
		formData = {
			// Basic Info (Required)
			employee_id: String(count + 1),
			username: '',
			// name: '',
			first_name: '',
			last_name: '',
			password: '',
			confirm_password: '',
			is_active: true,
			is_staff: false,
			is_superuser: false,

			// Contact Info (Optional)
			personal_phone: '',
			work_phone: '',
			personal_email: '',
			work_email: '',

			// Preferences (Optional)
			preferred_language: 'th',

			// Emergency Contact (Optional)
			emergency_contact_name: '',
			emergency_contact_phone: '',
			emergency_contact_email: ''
		};
		passwordFieldsEverTyped = false;
		activeTab = 0; // Reset to first tab

		passedStep1 = false;
		passedStep2 = false;
		passedStep3 = false;
		passedStep4 = false;

		// Reset password visibility states
		showPassword = false;
		showConfirmPassword = false;

		// Reset validation errors
		validationErrors = {
			personal_phone: '',
			personal_email: '',
			work_phone: '',
			work_email: ''
		};
	}

	// Combined handler for password input (existing functionality + alert dismissal)
	function handlePasswordInput() {
		passwordFieldsEverTyped = true;
		// dismissAlerts();
	}

	// Password visibility toggle functions
	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}

	function toggleConfirmPasswordVisibility() {
		showConfirmPassword = !showConfirmPassword;
	}

	// Tab navigation functions
	function nextTab() {
		if (activeTab < 3) {
			activeTab++;

			if (activeTab === 0) {
				passedStep1 = true;
			} else if (activeTab === 1) {
				passedStep2 = true;
			} else if (activeTab === 2) {
				passedStep3 = true;
			} else if (activeTab === 3) {
				passedStep4 = true;
			}
		}
	}

	function previousTab() {
		if (activeTab > 0) {
			activeTab--;

			if (activeTab === 0) {
				passedStep1 = true;
			} else if (activeTab === 1) {
				passedStep2 = true;
			} else if (activeTab === 2) {
				passedStep3 = true;
			} else if (activeTab === 3) {
				passedStep4 = true;
			}
		}
	}

	function goToTab(tabIndex: number) {
		if (tabIndex >= 0 && tabIndex <= 3) {
			activeTab = tabIndex;

			if (activeTab === 0) {
				passedStep1 = true;
			} else if (activeTab === 1) {
				passedStep2 = true;
			} else if (activeTab === 2) {
				passedStep3 = true;
			} else if (activeTab === 3) {
				passedStep4 = true;
			}
		}
	}

	// Helper function to open the modal
	function openSignUpModal() {
		signUpModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
		passwordFieldsEverTyped = false;
		// Reset the flag when opening modal
		shouldResetOnClose = true;
	}

	// Helper function to close the modal
	function closeSignUpModal() {
		if (shouldResetOnClose) resetFormData();
		signUpModalOpen = false;
	}

	$: enhanceOptions = {
		modalOpen: signUpModalOpen,
		setModalOpen: (value: boolean) => {
			signUpModalOpen = value;
			// If modal is closing and we should reset form, do it now
			if (!value && shouldResetOnClose) {
				resetFormData();
			}
		},
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => {
			successMessage = value;
			// Set flag to reset form when modal closes after successful submission
			shouldResetOnClose = true;
		},
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			// Parse error messages using the new utility function
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';

			if (
				fieldErrors.username ||
				// fieldErrors.name ||
				fieldErrors.first_name ||
				fieldErrors.last_name ||
				fieldErrors.password ||
				fieldErrors.confirm_password
			) {
				step1Valid = false;
			}

			if (fieldErrors.work_email) {
				step2Valid = false;
			}

			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// New properties for enhanced success behavior
		useToastOnSuccess: true,
		closeModalOnSuccess: true
	};
	export let count: number;

	let formData = {};
	$: resetFormData();

	// Password validation
	const specialChars = '!@#$%^&*';
	let passwordFieldsEverTyped = false;

	// Password visibility toggles
	let showPassword = false;
	let showConfirmPassword = false;

	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	$: passwordRulesStatus = checkPasswordRules(formData.password);
	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		formData.password === formData.confirm_password && formData.password.length > 0;

	// Step validation logic
	$: step1Valid =
		// formData.name.trim() !== '' &&
		formData.first_name.trim() !== '' &&
		formData.last_name.trim() !== '' &&
		formData.username.trim() !== '' &&
		allPasswordRulesPassed &&
		passwordsMatch &&
		!fieldErrors.username &&
		// !fieldErrors.name &&
		!fieldErrors.first_name &&
		!fieldErrors.last_name &&
		!fieldErrors.password &&
		!fieldErrors.confirm_password;

	// Check if there are any contact validation errors
	$: hasContactValidationErrors = Object.values(validationErrors).some((error) => error !== '');

	// Update step2Valid to include validation check
	$: step2Valid =
		formData.work_email.trim() !== '' && !hasContactValidationErrors && !fieldErrors.work_email;

	$: step3Valid = true; // All optional fields, always true

	$: step4Valid = true; // All optional fields, always true

	// Stepper steps configuration
	$: stepperSteps = [
		{
			id: 1,
			label: t('signup_form_tab_personal_info'),
			description: '',
			status:
				activeTab === 0
					? 'current'
					: activeTab !== 0 && step1Valid
						? 'completed'
						: activeTab !== 0 && !step1Valid
							? 'error'
							: 'pending',
			type: 'required'
		},
		{
			id: 2,
			label: t('signup_form_tab_contact_details'),
			description: '',
			status:
				activeTab === 1
					? 'current'
					: activeTab !== 1 && passedStep2 && step2Valid
						? 'completed'
						: activeTab !== 1 && passedStep2 && !step2Valid
							? 'error'
							: 'pending',
			type: 'optional'
		},
		{
			id: 3,
			label: t('signup_form_tab_preference'),
			description: '',
			status:
				activeTab === 2
					? 'current'
					: activeTab !== 2 && passedStep3 && step3Valid
						? 'completed'
						: 'pending',
			type: 'optional'
		},
		{
			id: 4,
			label: t('signup_form_tab_emergency_contact'),
			description: '',
			status:
				activeTab === 3
					? 'current'
					: activeTab !== 3 && passedStep4 && step4Valid
						? 'completed'
						: 'pending',
			type: 'optional'
		}
	];

	$: showPassword = (activeTab === 0)? showPassword : false;
	$: showConfirmPassword = (activeTab === 0)? showConfirmPassword : false;

	// Handle stepper step clicks
	function handleStepClick(stepId: number) {
		goToTab(stepId - 1);
	}

	interface EnhanceOptions {
		modalOpen: boolean;
		setModalOpen?: (value: boolean) => void;
		setPending?: (value: boolean) => void;
		setShowSuccessMessage?: (value: boolean) => void;
		setSuccessMessage?: (value: string) => void;
		setShowErrorMessage?: (value: boolean) => void;
		setErrorMessage?: (value: string) => void;
		// New optional properties for enhanced behavior
		useToastOnSuccess?: boolean;
		closeModalOnSuccess?: boolean;
	}

	function handleEnhance(options: EnhanceOptions) {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			// Set pending state if the option is provided
			options.setPending?.(true);

			if (result.type === 'failure') {
				options.setErrorMessage?.(result.data?.error || 'Operation failed');
				// activeTab = 0;
				// Don't close modal on error
			} else if (result.type === 'success') {
				// const successMessage = result.data.res_msg || 'Status : Operation success';
				const successMessage = t('signup_success_toast');

				if (options.useToastOnSuccess) {
					toastStore.add(successMessage, 'success');
				} else {
					// Existing behavior for backward compatibility
					options.setShowSuccessMessage?.(true);
					options.setSuccessMessage?.(successMessage);
				}

				if (options.closeModalOnSuccess) {
					options.setModalOpen?.(false);
				}
			}
			// Update the page data
			await update();

			// Reset pending state if the option is provided
			options.setPending?.(false);
		};
	}
</script>

<Button
	size="sm"
	class="bg-green-600 text-white hover:bg-green-700"
	on:click={() => openSignUpModal()}
>
	+ {t('new_account')}
</Button>

<Modal
	bind:open={signUpModalOpen}
	on:close={() => closeSignUpModal()}
	size="lg"
	class="h-[80vh] overflow-y-auto"
>
	<h3 slot="header">{t('create_account')}</h3>
	{#if showSuccessMessage}
		<Alert color="green" class="mb-4">
			{successMessage}
		</Alert>
	{/if}

	<!-- Field-specific error display for multi-field errors -->
	{#if Object.keys(fieldErrors).length > 0}
		{#each Object.entries(fieldErrors) as [fieldName, errors]}
			{#each errors as error}
				<Alert color="red" class="mb-4">
					{#if fieldName === 'username'}
						{t('username')}:
						{t('signup_error_duplicated_username')}
					{:else if fieldName === 'work_email'}
						{t('signup_form_work_email')}:
						{t('signup_error_duplicated_email')}
					{/if}
				</Alert>
			{/each}
		{/each}
	{/if}

	<!-- Stepper Navigation -->
	<div class="mb-6">
		<div class="relative">
			<!-- Continuous Connector Line Background -->
			<div
				class="absolute left-5 right-5 top-5 h-0.5 bg-gray-300"
				style="left: calc(12.5%); width: calc(75%);}"
			></div>

			<!-- Progress Line (shows completion progress) -->
			<div
				class="absolute top-5 h-0.5 bg-gray-300 transition-all duration-300"
				style="left: calc(12.5%); width: {activeTab > 0 ? `${(activeTab / 3) * 75}%` : '0%'}"
			></div>

			<!-- Step Buttons Grid -->
			<div class="relative z-10 grid grid-cols-4 gap-4">
				{#each stepperSteps as step}
					<div class="flex flex-col items-center">
						<!-- Step Circle -->
						<button
							type="button"
							class="flex h-10 w-10 items-center justify-center rounded-full border-2 text-sm font-semibold transition-colors
								{step.status === 'completed'
								? 'border-green-500 bg-green-500 text-white hover:bg-green-600'
								: step.status === 'current'
									? 'border-blue-500 bg-blue-500 text-white'
									: step.status === 'pending'
										? 'border-gray-300 bg-white text-gray-500 hover:border-gray-400'
										: step.status === 'error'
											? 'border-red-500 bg-red-500 text-white hover:bg-red-600'
											: ''}"
							on:click={() => handleStepClick(step.id)}
							aria-label="Go to {step.label}"
						>
							{#if step.status === 'completed'}
								<CheckOutline class="h-5 w-5" />
							{:else if step.status === 'error'}
								<CloseOutline class="h-5 w-5" />
							{:else}
								{step.id}
							{/if}
						</button>

						<!-- Step Label (Hidden on mobile) -->
						<div class="mt-2 hidden text-center sm:hidden md:block lg:block">
							<div
								class="text-sm font-medium {step.status === 'current'
									? 'text-blue-600'
									: step.status === 'completed'
										? 'text-green-600'
										: step.status === 'error'
											? 'text-red-600'
											: 'text-gray-500'}"
							>
								<p class="leading-tight">{step.label}</p>
							</div>
							{#if step.description}
								<div class="mt-1 text-xs text-gray-400">{step.description}</div>
							{/if}
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Mobile Step Label -->
		<div class="mt-4 text-center sm:hidden">
			<div class="text-sm font-medium text-blue-600">{stepperSteps[activeTab].label}</div>
			{#if stepperSteps[activeTab].description}
				<div class="mt-1 text-xs text-gray-400">{stepperSteps[activeTab].description}</div>
			{/if}
		</div>
	</div>

	<!-- Tab Content Indicators -->
	{#if activeTab === 0}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
				* {t('signup_form_required_badge')}</span
			>
		</div>
	{:else if activeTab === 1}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
				* {t('signup_form_required_badge')}</span
			>
		</div>
	{:else if activeTab === 2}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>{t('signup_form_optional_badge')}</span
			>
		</div>
	{:else if activeTab === 3}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>{t('signup_form_optional_badge')}</span
			>
		</div>
	{/if}

	<form
		bind:this={signUpForm}
		action="?/sign_up_user"
		method="POST"
		use:enhance={() => handleEnhance(enhanceOptions)}
		class="space-y-4 overflow-y-auto"
	>
		<!-- Basic Info Tab Content -->
		{#if activeTab === 0}
			<!-- <div>
				<Label for="employee_id" class="space-y-2">
					{t('employee_id')}
				</Label>
				<Input
					id="employee_id"
					name="employee_id"
					type="text"
					bind:value={formData.employee_id}
					required
					disabled
					readonly
				/>
			</div> -->

			<div>
				<div>
					<Label for="username" class="mb-1 space-y-2">
						{t('username')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<Input
						id="username"
						name="username"
						type="text"
						maxlength={25}
						bind:value={formData.username}
						on:input={(e) => handleNameInput(e, 'username')}
						required
					/>
					{#if fieldErrors.username}
						{#each fieldErrors.username as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								<!-- {error} -->
								{t('signup_error_duplicated_username')}
							</Alert>
						{/each}
					{/if}
				</div>
				<!-- <div>
					<Label for="name" class="mb-1 space-y-2">
						{t('display_name')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<Input
						id="name"
						name="name"
						type="text"
						maxlength={20}
						bind:value={formData.name}
						on:input={(e) => handleNameInput(e, 'name')}
						required
					/>
				</div> -->
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="first_name" class="mb-1 space-y-2">
						{t('first_name')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<Input
						id="first_name"
						name="first_name"
						type="text"
						maxlength={50}
						bind:value={formData.first_name}
						on:input={(e) => handleNameInput(e, 'first_name')}
						required
					/>
					{#if fieldErrors.first_name}
						{#each fieldErrors.first_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>

				<div>
					<Label for="last_name" class="mb-1 space-y-2">
						{t('last_name')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<Input
						id="last_name"
						name="last_name"
						type="text"
						maxlength={50}
						bind:value={formData.last_name}
						on:input={(e) => handleNameInput(e, 'last_name')}
						required
					/>
					{#if fieldErrors.last_name}
						{#each fieldErrors.last_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="password" class="mb-1 space-y-2">
						{t('password')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<div class="relative">
						<Input
							id="password"
							name="password"
							type={showPassword ? 'text' : 'password'}
							maxlength={30}
							bind:value={formData.password}
							on:input={handlePasswordInput}
							required
							class="pr-10"
						/>
						<button
							type="button"
							class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
							on:click={togglePasswordVisibility}
							aria-label={showPassword ? 'Hide password' : 'Show password'}
							tabindex="-1"
						>
							{#if showPassword}
								<EyeSlashSolid class="h-5 w-5" />
							{:else}
								<EyeSolid class="h-5 w-5" />
							{/if}
						</button>
					</div>
					{#if fieldErrors.password}
						{#each fieldErrors.password as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}

					<div class="mb-1 mt-2 text-xs font-normal text-gray-400">
						{t('password_validation_msg_1')}
					</div>
					<ul class="space-y-0 text-xs">
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.length
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_2')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_3')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.number
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_4')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.special
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_5')} ({specialChars})</span
							>
						</li>
					</ul>
				</div>

				<div>
					<Label for="confirm_password" class="mb-1 space-y-2">
						{t('confirm_password')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<div class="relative">
						<Input
							id="confirm_password"
							name="confirm_password"
							type={showConfirmPassword ? 'text' : 'password'}
							maxlength={30}
							bind:value={formData.confirm_password}
							on:input={handlePasswordInput}
							required
							class="pr-10"
						/>
						<button
							type="button"
							class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
							on:click={toggleConfirmPasswordVisibility}
							aria-label={showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'}
							tabindex="-1"
						>
							{#if showConfirmPassword}
								<EyeSlashSolid class="h-5 w-5" />
							{:else}
								<EyeSolid class="h-5 w-5" />
							{/if}
						</button>
					</div>
					<div style="min-height:1em;" class="justify-left items-top mt-2 flex">
						{#if passwordFieldsEverTyped && !passwordsMatch && formData.confirm_password.length > 0}
							<span class="text-left text-xs text-red-600"
								>{t('password_validation_msg_do_not_match')}</span
							>
						{/if}
					</div>
					{#if fieldErrors.confirm_password}
						{#each fieldErrors.confirm_password as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>

			<!-- <div>
				<Label for="department" class="space-y-2">Department</Label>
				<Input
					id="department"
					name="department"
					type="text"
					bind:value={formData.department}
				/>
			</div>

			<div>
				<Label for="role" class="space-y-2">Role</Label>
				<Select 
					id="role" 
					name="role" 
					bind:value={formData.role}
					required
				>
					{#each roles as role}
						<option value={role}>{role}</option>
					{/each}
				</Select>
			</div> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active} bind:value={formData.is_active}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active}>
				Enable Active Status
			</Checkbox>
			<input type="hidden" name="is_active" value={formData.is_active}> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff} bind:value={formData.is_staff}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff}>
				Enable Staff
			</Checkbox>                 -->
			<!-- <input type="hidden" name="is_staff" value={formData.is_staff}> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser} bind:value={formData.is_superuser}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser}>
				Enable Superuser
			</Checkbox>
			<input type="hidden" name="is_superuser" value={formData.is_superuser}> -->
		{/if}

		<!-- Contact Tab Content -->
		{#if activeTab === 1}
			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="work_phone" class="mb-1 space-y-2">
						{t('signup_form_work_phone')}
					</Label>
					<Input
						id="work_phone"
						name="work_phone"
						type="text"
						maxlength={20}
						bind:value={formData.work_phone}
						on:input={(e) => handlePhoneInput(e, 'work_phone')}
					/>
					{#if validationErrors.work_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.work_phone}
						</Alert>
					{/if}
				</div>

				<div>
					<Label for="work_email" class="mb-1 space-y-2">
						{t('signup_form_work_email')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<Input
						id="work_email"
						name="work_email"
						type="email"
						maxlength={30}
						bind:value={formData.work_email}
						on:input={(e) => handleEmailInput(e, 'work_email')}
					/>
					{#if validationErrors.work_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.work_email}
						</Alert>
					{/if}
					{#if fieldErrors.work_email}
						{#each fieldErrors.work_email as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								<!-- {error} -->
								{t('signup_error_duplicated_email')}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>
			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="personal_phone" class="mb-1 space-y-2">
						{t('signup_form_personal_phone')}
					</Label>
					<Input
						id="personal_phone"
						name="personal_phone"
						type="text"
						maxlength={20}
						bind:value={formData.personal_phone}
						on:input={(e) => handlePhoneInput(e, 'personal_phone')}
					/>
					{#if validationErrors.personal_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.personal_phone}
						</Alert>
					{/if}
				</div>

				<div>
					<Label for="personal_email" class="mb-1 space-y-2">
						{t('signup_form_personal_email')}
					</Label>
					<Input
						id="personal_email"
						name="personal_email"
						type="email"
						maxlength={30}
						bind:value={formData.personal_email}
						on:input={(e) => handleEmailInput(e, 'personal_email')}
					/>
					{#if validationErrors.personal_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.personal_email}
						</Alert>
					{/if}
				</div>
			</div>
		{/if}

		<!-- Preferences Tab Content -->
		{#if activeTab === 2}
			<div>
				<Label for="preferred_language" class="mb-1 space-y-2">
					{t('signup_form_preferred_language')}
				</Label>
				<Select
					id="preferred_language"
					name="preferred_language"
					bind:value={formData.preferred_language}
					items={languageOptions}
					placeholder={t('signup_form_preferred_language_placeholder')}
				/>
			</div>
		{/if}

		<!-- Emergency Tab Content -->
		{#if activeTab === 3}
			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="emergency_contact_name" class="mb-1 space-y-2">
						{t('signup_form_emergency_name')}
					</Label>
					<Input
						id="emergency_contact_name"
						name="emergency_contact_name"
						type="text"
						maxlength={50}
						bind:value={formData.emergency_contact_name}
					/>
				</div>
				<div>
					<Label for="emergency_contact_phone" class="mb-1 space-y-2">
						{t('signup_form_emergency_phone')}
					</Label>
					<Input
						id="emergency_contact_phone"
						name="emergency_contact_phone"
						type="tel"
						maxlength={20}
						bind:value={formData.emergency_contact_phone}
					/>
				</div>

				<!-- <div>
					<Label for="emergency_contact_email" class="mb-1 space-y-2">
						{t('signup_form_emergency_email')}
					</Label>
					<Input
						id="emergency_contact_email"
						name="emergency_contact_email"
						type="email"
						maxlength={30}
						bind:value={formData.emergency_contact_email}
					/>
				</div> -->
			</div>
		{/if}

		<!-- Hidden inputs to ensure all form data is submitted regardless of active tab -->
		<!-- This fixes the issue where only fields from the current active tab were being submitted -->

		<!-- Basic Info fields (when not on tab 0) -->
		{#if activeTab !== 0}
			<input type="hidden" name="employee_id" value={formData.employee_id} />
			<input type="hidden" name="username" value={formData.username} />
			<input type="hidden" name="name" value={formData.name} />
			<input type="hidden" name="first_name" value={formData.first_name} />
			<input type="hidden" name="last_name" value={formData.last_name} />
			<input type="hidden" name="password" value={formData.password} />
			<input type="hidden" name="confirm_password" value={formData.confirm_password} />
		{/if}

		<!-- Contact Info fields (when not on tab 1) -->
		{#if activeTab !== 1}
			<input type="hidden" name="personal_phone" value={formData.personal_phone} />
			<input type="hidden" name="personal_email" value={formData.personal_email} />
			<input type="hidden" name="work_phone" value={formData.work_phone} />
			<input type="hidden" name="work_email" value={formData.work_email} />
		{/if}

		<!-- Preferences fields (when not on tab 2) -->
		{#if activeTab !== 2}
			<input type="hidden" name="preferred_language" value={formData.preferred_language} />
		{/if}

		<!-- Emergency Contact fields (when not on tab 3) -->
		{#if activeTab !== 3}
			<input type="hidden" name="emergency_contact_name" value={formData.emergency_contact_name} />
			<input
				type="hidden"
				name="emergency_contact_phone"
				value={formData.emergency_contact_phone}
			/>
			<input
				type="hidden"
				name="emergency_contact_email"
				value={formData.emergency_contact_email}
			/>
		{/if}

		<!-- Always include hidden fields -->
		<input type="hidden" name="is_active" value={formData.is_active} />
		<input type="hidden" name="is_staff" value={formData.is_staff} />
		<input type="hidden" name="is_superuser" value={formData.is_superuser} />
	</form>
	<svelte:fragment slot="footer">
		<div class="flex w-full justify-between">
			<div class="flex gap-2">
				<Button color="alternative" on:click={() => closeSignUpModal()}>{t('cancel')}</Button>
			</div>

			<div class="flex gap-2">
				{#if activeTab < 3}
					{#if activeTab > 0}
						<Button color="light" on:click={previousTab}>{t('previous')}</Button>
					{/if}
					<Button color="blue" on:click={nextTab}>{t('next')}</Button>
				{:else}
					<Button color="light" on:click={previousTab}>{t('previous')}</Button>
					<Button
						type="submit"
						color="blue"
						disabled={!allPasswordRulesPassed || !passwordsMatch || !step1Valid || !step2Valid}
						on:click={() => signUpForm.requestSubmit()}
					>
						{t('create_account')}
					</Button>
				{/if}
			</div>
		</div>
	</svelte:fragment>
</Modal>
