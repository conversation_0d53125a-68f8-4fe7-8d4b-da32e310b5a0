<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Alert, Select, Label, Textarea, Drawer, Radio } from 'flowbite-svelte';
	import { InfoCircleSolid } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { Dropdown, DropdownItem } from 'flowbite-svelte';
	import { ChevronDownOutline } from 'flowbite-svelte-icons';
	import { MultiSelect } from 'flowbite-svelte';

	export let ticket: any;
	export let statuses: [
        { id: 1, name: 'default' },
        { id: 2, name: 'open' },
        { id: 3, name: 'assigned' },
        { id: 4, name: 'waiting' },
        { id: 5, name: 'pending_to_close' },
        { id: 6, name: 'closed' }
    ];
	export let ticket_topics: any[];
	export let isDropDownItem: boolean = false;
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	// Get unique case_type and case_topic lists
	const caseTypes = [...new Set(ticket_topics.map((item) => item.case_type))];
	const caseTopics = [...new Set(ticket_topics.map((item) => item.case_topic))];

	let changeTicketStatusForm: HTMLFormElement;
	let changeTicketStatusModalOpen = false;
	let changingTicketStatus: any = null;
	let selectedTicketStatusId: number = 0;
	let selectedTicketCaseType: string = '';
	let selectedTicketCaseTopic: string[] = [];
    let statusChanged: boolean = false;
    let initialStatusId: number = 0;

	// State variables for handling messages - REMOVED, using toast instead
	// let showSuccessMessage = false;
	// let showErrorMessage = false;
	// let successMessage = '';
	// let errorMessage = '';

	function openChangeTicketStatusModal(ticket: any) {
		changingTicketStatus = { ...ticket };
        initialStatusId = ticket.status.id;
        selectedTicketStatusId = ticket.status.id;
		changeTicketStatusModalOpen = true;
        statusChanged = false;
		// Reset messages when opening modal - REMOVED, using toast instead
		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';
		hidden3 = false;
	}

	function handleChangeTicketStatusSubmit(event: Event) {
		// Reset messages - REMOVED, using toast instead
		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';
        
        // Check if status has been changed
        if (selectedTicketStatusId === initialStatusId) {
            event.preventDefault();
            // Use toast for error message
            import('$lib/stores/toastStore').then(({ toastStore }) => {
                toastStore.add("Please select a different status or cancel.", 'error');
            });
            return;
        }
	}

	$: enhanceOptions = {
		modalOpen: changeTicketStatusModalOpen,
		setModalOpen: (value: boolean) => (changeTicketStatusModalOpen = value),
		useToastOnSuccess: true,
		useToastOnError: true,
		closeModalOnSuccess: true,
		onSuccess: onSuccess // Add the success callback
	};

	// Reactive statement to filter case topics based on selected case type
	$: filteredCaseTopics = ticket_topics
		.filter((topic) => topic.case_type === selectedTicketCaseType)
		.map((topic) => ({ value: topic.case_topic, name: topic.case_topic }));

	// Reset selectedCaseTopic when selectedCaseType changes
	$: if (selectedTicketCaseType !== '') {
		selectedTicketCaseTopic = []; // Reset case topic array
	}

    // Track when status changes
    $: {
        if (selectedTicketStatusId !== initialStatusId && initialStatusId !== 0) {
            statusChanged = true;
        } else {
            statusChanged = false;
        }
    }

	// Check if both case type and case topic are selected
	$: isSelectionValid = selectedTicketCaseType !== '' && selectedTicketCaseTopic.length > 0;
    
    // Check if form is valid for submission
    // Check if we're trying to close a ticket that's not already closed
    $: isClosingTicket = selectedTicketStatusId === 6 && initialStatusId !== 6;


    // Check if form is valid for submission
    $: isFormValid = 
            statusChanged && 
            (selectedTicketStatusId !== 3 || (isClosingTicket && isSelectionValid));

	import { sineIn } from 'svelte/easing';
	import type { ListEvents } from 'flowbite-svelte/List.svelte';
	let hidden3 = true;
	let transitionParams = {
		x: -320,
		duration: 200,
		easing: sineIn
	};

	// Function to get status color classes
	function getStatusColorClasses(statusId: number) {
		switch (statusId) {
			case 2: // 'open'
				return 'border-green-200 bg-green-100 text-green-700';
			case 3: // 'assigned'
				return 'border-blue-200 bg-blue-100 text-blue-700';
			case 4: // 'waiting'
				return 'border-yellow-200 bg-yellow-200 text-yellow-700';
			case 5: // 'pending to close'
				return 'border-gray-200 bg-gray-200 text-gray-700';
			case 6: // 'closed'
				return 'border-gray-200 bg-gray-200 text-gray-700';
			default:
				return 'border-gray-200 bg-gray-100 text-gray-700';
		}
	}

    // Get status name by ID
    function getStatusNameById(statusId: number) {
        const status = statuses.find(s => s.id === statusId);
        // return status ? status.name.charAt(0).toUpperCase() + status.name.slice(1) : 'Unknown';
        return status ? status.name.charAt(0).toUpperCase() + status.name.slice(1).split('_').join(' ') : 'Unknown';
    }

    // Update the getTicketTopicId function to handle the array of case topics
    function getTicketTopicId(caseType: string, caseTopics: string[]) {
        return ticket_topics
            .filter(topic => caseTopics.includes(topic.case_topic) && topic.case_type === caseType)
            .map(topic => topic.id);
    }

    // Add this reactive statement to debug
    $: {
        console.log('selectedTicketStatusId:', selectedTicketStatusId, typeof selectedTicketStatusId);
        console.log('initialStatusId:', initialStatusId, typeof initialStatusId);
        console.log('isClosingTicket:', isClosingTicket);
    }
</script>

{#if isDropDownItem}
	<Button
		color="none"
		class="w-full justify-start text-left hover:bg-gray-100"
		on:click={() => openChangeTicketStatusModal(ticket)}
	>
		{t('change_status')}
	</Button>
{:else}
	<Button
		size="xs"
		class="bg-black text-white"
		color="black"
		on:click={() => openChangeTicketStatusModal(ticket)}
	>
		{t('status')}
	</Button>
{/if}

<Modal bind:open={changeTicketStatusModalOpen} size="md" autoclose={false} class="w-full">
    <h2 slot="header" class="inline-flex items-center gap-4">
        <div class="inline-flex items-center">
            <InfoCircleSolid class="me-2.5 h-5 w-5" />{t('ticket_status')}
        </div>
        <span class={`px-3 py-1 inline-flex text-sm font-medium rounded-full ${getStatusColorClasses(initialStatusId)}`}>
            {getStatusNameById(initialStatusId)}
        </span>
    </h2>

	{#if changingTicketStatus}
        
        <!-- Current Status Badge -->
        <!-- <div class="mb-4">
            <p class="text-sm font-medium text-gray-700 mb-2">{t('current_status')}:</p>
            <span class={`px-3 py-1 inline-flex text-sm font-medium rounded-full ${getStatusColorClasses(initialStatusId)}`}>
                {getStatusNameById(initialStatusId)}
            </span>
        </div> -->
        
		<form
			bind:this={changeTicketStatusForm}
			action="?/change_ticket_status"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleChangeTicketStatusSubmit}
		>
			<input type="hidden" name="ticket_id" value={changingTicketStatus.id} />
			<div>
                <!-- Status Selection with Radio Buttons -->
                <label class="mb-3 block text-sm font-medium text-gray-700">
                    {t('select_new_ticket_status')}
                </label>
                <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
                    {#each statuses.sort((a, b) => {
                        // Make "closed" (id: 3) appear last
                        if (a.id === 3) return 1;
                        if (b.id === 3) return -1;
                        return a.id - b.id;
                    }) as status}
                        <!-- Prevent selecting default status -->
                        {#if status.name != 'default' && status.name != 'waiting' && status.name != 'pending_to_close' && status.name != 'assigned'}
                            <div class={`rounded-md border ${getStatusColorClasses(status.id)} ${status.id === initialStatusId ? 'opacity-50' : ''}`}>
                                <Radio 
                                    name="ticketStatus" 
                                    value={status.id} 
                                    bind:group={selectedTicketStatusId} 
                                    class="w-full p-4"
                                    disabled={status.id === initialStatusId}
                                >
                                    <!-- {status.name.charAt(0).toUpperCase() + status.name.slice(1)} -->
                                    <!-- {status.name} -->
                                    {t(status.name)}
                                    {#if status.id === initialStatusId}
                                        <span class="ml-1">({t('current')})</span>
                                    {/if}
                                </Radio>
                            </div>
                        {/if}
                    {/each}
                </div>

                <!-- Case Type and Topic sections (only show when changing status to closed) -->
				{#if isClosingTicket}
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md mb-4">
                        <p class="text-sm text-blue-800 mb-2">
                            <strong>{t('additional_info_required')}</strong>
                        </p>
                    </div>
                    
                    <!-- Case Type section with radio buttons -->
                    <label class="mb-3 block text-sm font-medium text-gray-700">
                        {t('select_case_type')}
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
                        {#each caseTypes as caseType}
                            <div class="rounded-md border border-gray-200 bg-gray-100">
                                <Radio 
                                    name="caseType" 
                                    value={caseType} 
                                    bind:group={selectedTicketCaseType} 
                                    class="w-full p-3"
                                >
                                    {caseType}
                                </Radio>
                            </div>
                        {/each}
                    </div>

                    <!-- Case Topic section with multi-selectable boxes -->
                    <label class="mb-3 block text-sm font-medium text-gray-700">
                        {t('case_topic_multiple')}
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-2 gap-3">
                        {#each filteredCaseTopics as caseTopic}
                            {@const isSelected = selectedTicketCaseTopic.includes(caseTopic.value)}
                            <button 
                                type="button"
                                class="border rounded-md p-3 text-sm text-left {isSelected ? 'bg-blue-100 border-blue-500' : 'bg-gray-50 border-gray-200'}"
                                on:click={() => {
                                    if (isSelected) {
                                        // Remove from selection if already selected
                                        selectedTicketCaseTopic = selectedTicketCaseTopic.filter(topic => topic !== caseTopic.value);
                                    } else {
                                        // Add to selection
                                        selectedTicketCaseTopic = [...selectedTicketCaseTopic, caseTopic.value];
                                    }
                                }}
                            >
                                {caseTopic.name}
                            </button>
                        {/each}
                    </div>
                    
                    {#if selectedTicketCaseType !== '' && selectedTicketCaseTopic.length === 0}
                        <p class="mt-2 text-sm text-red-600">
                            {t('please_select_case_topic')}
                        </p>
                    {/if}
				{/if}

				<input type="hidden" name="new_status_id" value={selectedTicketStatusId} />
				<input
					type="hidden"
					name="new_ticket_topic"
					value={getTicketTopicId(selectedTicketCaseType, selectedTicketCaseTopic)}
				/>
			</div>
		</form>
	{/if}

	<!-- Confirm and Cancel Button -->
	<svelte:fragment slot="footer">
		<Button 
            color="dark" 
            on:click={() => changeTicketStatusForm.requestSubmit()}
            disabled={!isFormValid}
            class={!isFormValid ? "opacity-50 cursor-not-allowed" : ""}
        >
            {t('confirm')}
        </Button>
		<Button color="none" on:click={() => (changeTicketStatusModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>