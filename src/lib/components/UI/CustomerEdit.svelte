<script lang="ts">
    import { t, language } from '$src/lib/stores/i18n';
    import { enhance } from '$app/forms';
    import {
        Button,
        Modal,
        Label,
        Input,
        Alert,
        Select,
    } from 'flowbite-svelte';
    import countries from 'i18n-iso-countries';
    import en from 'i18n-iso-countries/langs/en.json';
    import th from 'i18n-iso-countries/langs/th.json';
    
    // Register the locales
    countries.registerLocale(en);
    countries.registerLocale(th);
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
    import { page } from '$app/stores';

    export let customer: any;

    // console.log('CustomerEdit component initialized with customer:', customer);

    let editForm: HTMLFormElement;
    let editModalOpen = false;
    let selectInstances: any = null;
    
    // State variables for handling messages
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    $: role = $page.data.role;
    $: isAgent = role === 'Agent';

    let formData: any = {};

    // Get country options based on current language
    $: countryOptions = Object.entries(countries.getNames($language || 'en'))
        .map(([code, name]) => ({ value: code, name }))
        .sort((a, b) => a.name.localeCompare(b.name));

    // Gender options - Fixed to match the backend field name
    const genderOptions = [
        { value: 1, label: 'not_specified' },
        { value: 2, label: 'male' },
        { value: 3, label: 'female' },
        { value: 4, label: 'other' }
    ];

    // Language options - using lowercase codes that API expects
    const languageOptions = [
        { value: 'th', label: 'Thai' },
        { value: 'en', label: 'English' },
        { value: 'zh', label: 'Chinese' },
        { value: 'jp', label: 'Japanese' }
    ];

    // Contact method options - using lowercase values that API expects
    const contactMethodOptions = [
        { value: 'EMAIL', label: 'Email' },
        { value: 'PHONE', label: 'Phone Call' },
        { value: 'SMS', label: 'SMS' },
        { value: 'LINE', label: 'LINE' },
        { value: 'WHATSAPP', label: 'WhatsApp' }
    ];

    function maskPhoneNumber(phone: string): string {
        if (!phone) return '';
        if (isAgent) {
            const len = phone.length;
            if (len <= 4) return phone;
            return phone.slice(0, 3) + 'x'.repeat(len - 5) + phone.slice(len - 2);
        }
        return phone;
    }

    function openEditModal(customer: any) {
        selectInstances = { ...customer };
        editModalOpen = true;
        // Reset messages when opening modal
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';

        // Initialize formData with existing values - Fixed to properly handle nested address object
        formData = {
            // Original fields
            name: customer.name || '',
            email: customer.email || '',
            date_of_birth: customer.date_of_birth || '',
            phone: isAgent ? maskPhoneNumber(customer.phone || '') : (customer.phone || ''),
            
            // Address fields - Extract from nested address object
            address_line1: customer.address?.address_line1 || '',
            address_line2: customer.address?.address_line2 || '',
            city: customer.address?.city || '',
            state_province_region: customer.address?.state_province_region || '',
            zip_code: customer.address?.zip_code || '',
            country: customer.address?.country || customer.country || '',
            
            // New fields - Fixed field mappings
            first_name: customer.first_name || '',
            last_name: customer.last_name || '',
            middle_name: customer.middle_name || '',
            nickname: customer.nickname || '',
            gender_id: customer.gender_id || customer.gender || 1, // Handle both field names
            nationality: customer.nationality || '',
            national_id: customer.national_id || '',
            passport_number: customer.passport_number || '',
            career: customer.career || '',
            preferred_language: customer.preferred_language || 'th',
            preferred_contact_method: customer.preferred_contact_method || 'EMAIL'
        };

        console.log('Form data initialized:', formData);
    }

    function handleEditSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: editModalOpen,
        setModalOpen: (value: boolean) => editModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value,
        onSuccess: (result: any) => {
            // Update the customer prop with new data if returned from server
            if (result?.data?.customer) {
                customer = result.data.customer;
                // Re-initialize form data with updated customer data
                openEditModal(customer);
            } else {
                // If no updated data returned, reload the page to get fresh data
                window.location.reload();
            }
        }
    };
</script>

<Button class="text-gray-700 bg-white border hover:bg-gray-100 flex items-center gap-2 shadow-md" on:click={() => openEditModal(customer)}>
    {t('edit_customer')}
</Button>

<Modal bind:open={editModalOpen} size="xl" title="{t('edit_customer')}">
    <svelte:fragment slot="header">
        <h2>{t('edit_customer')}</h2>
    </svelte:fragment>
    
    {#if selectInstances}
        {#if showSuccessMessage}
            <Alert color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        
        <form
            bind:this={editForm}
            action="?/update_customer"
            method="POST"
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handleEditSubmit}
        >
            <input type="hidden" name="customer_id" value={customer.customer_id}>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- First Column: Personal Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">{t('personal_information')}</h3>
                    
                    <div>
                        <Label for="name" class="font-medium">{t('display_name')}</Label>
                        <Input 
                            id="name" 
                            name="name" 
                            type="text" 
                            bind:value={formData.name} 
                            class="mt-1"
                            readonly
                        />
                    </div>

                    <div>
                        <Label for="first_name" class="font-medium">{t('first_name')}</Label>
                        <Input 
                            id="first_name" 
                            name="first_name" 
                            type="text" 
                            bind:value={formData.first_name} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="middle_name" class="font-medium">{t('middle_name')}</Label>
                        <Input 
                            id="middle_name" 
                            name="middle_name" 
                            type="text" 
                            bind:value={formData.middle_name} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="last_name" class="font-medium">{t('last_name')}</Label>
                        <Input 
                            id="last_name" 
                            name="last_name" 
                            type="text" 
                            bind:value={formData.last_name} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="nickname" class="font-medium">{t('nickname')}</Label>
                        <Input 
                            id="nickname" 
                            name="nickname" 
                            type="text" 
                            bind:value={formData.nickname} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="gender_id" class="font-medium">{t('gender')}</Label>
                        <Select 
                            id="gender_id" 
                            name="gender_id" 
                            bind:value={formData.gender_id} 
                            class="mt-1"
                        >
                            {#each genderOptions as option}
                                <option value={option.value}>{t(option.label)}</option>
                            {/each} 
                        </Select>
                    </div>
                    
                    <div>
                        <Label for="date_of_birth" class="font-medium">{t('date_of_birth')}</Label>
                        <Input 
                            id="date_of_birth" 
                            name="date_of_birth" 
                            type="date" 
                            bind:value={formData.date_of_birth} 
                            class="mt-1" 
                        />
                    </div>
                </div>

                <!-- Second Column: Contact & Identity Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">{t('contact_identity_information')}</h3>
                    
                    <div>
                        <Label for="phone" class="font-medium">{t('phone_number')}</Label>
                        <Input 
                            id="phone" 
                            name="phone" 
                            type="text" 
                            bind:value={formData.phone} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="email" class="font-medium">{t('email')}</Label>
                        <Input 
                            id="email" 
                            name="email" 
                            type="email" 
                            bind:value={formData.email} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="nationality" class="font-medium">{t('nationality')}</Label>
                        <Select 
                            id="nationality" 
                            name="nationality" 
                            bind:value={formData.nationality} 
                            class="mt-1"
                        >
                            <option value="">{t('select_nationality')}</option>
                            {#each countryOptions as country}
                                <option value={country.value}>{country.name}</option>
                            {/each}
                        </Select>
                    </div>
                    
                    <div>
                        <Label for="national_id" class="font-medium">{t('national_id')}</Label>
                        <Input 
                            id="national_id" 
                            name="national_id" 
                            type="text" 
                            bind:value={formData.national_id} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="passport_number" class="font-medium">{t('passport_number')}</Label>
                        <Input 
                            id="passport_number" 
                            name="passport_number" 
                            type="text" 
                            bind:value={formData.passport_number} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="career" class="font-medium">{t('career')}</Label>
                        <Input 
                            id="career" 
                            name="career" 
                            type="text" 
                            bind:value={formData.career} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="preferred_language" class="font-medium">{t('preferred_language')}</Label>
                        <Select 
                            id="preferred_language" 
                            name="preferred_language" 
                            bind:value={formData.preferred_language} 
                            class="mt-1"
                        >
                            <option value="">{t('select_language')}</option>
                            {#each languageOptions as lang}
                                <option value={lang.value}>{lang.label}</option>
                            {/each}
                        </Select>
                    </div>
                    
                    <div>
                        <Label for="preferred_contact_method" class="font-medium">{t('preferred_contact_method')}</Label>
                        <Select 
                            id="preferred_contact_method" 
                            name="preferred_contact_method" 
                            bind:value={formData.preferred_contact_method} 
                            class="mt-1"
                        >
                            <option value="">{t('select_contact_method')}</option>
                            {#each contactMethodOptions as method}
                                <option value={method.value}>{method.label}</option>
                            {/each}
                        </Select>
                    </div>
                </div>

                <!-- Third Column: Address Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">{t('address_information')}</h3>
                    
                    <div>
                        <Label for="address_line1" class="font-medium">{t('address_line1')}</Label>
                        <Input 
                            id="address_line1" 
                            name="address_line1" 
                            type="text" 
                            bind:value={formData.address_line1} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="address_line2" class="font-medium">{t('address_line2')}</Label>
                        <Input 
                            id="address_line2" 
                            name="address_line2" 
                            type="text" 
                            bind:value={formData.address_line2} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="city" class="font-medium">{t('city')}</Label>
                        <Input 
                            id="city" 
                            name="city" 
                            type="text" 
                            bind:value={formData.city} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="state_province_region" class="font-medium">{t('state_province_region')}</Label>
                        <Input 
                            id="state_province_region" 
                            name="state_province_region" 
                            type="text" 
                            bind:value={formData.state_province_region} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="zip_code" class="font-medium">{t('zip_code')}</Label>
                        <Input 
                            id="zip_code" 
                            name="zip_code" 
                            type="text" 
                            bind:value={formData.zip_code} 
                            class="mt-1" 
                        />
                    </div>
                    
                    <div>
                        <Label for="country" class="font-medium">{t('country')}</Label>
                        <Select 
                            id="country" 
                            name="country" 
                            bind:value={formData.country} 
                            class="mt-1"
                        >
                            <option value="">{t('select_country')}</option>
                            {#each countryOptions as country}
                                <option value={country.value}>{country.name}</option>
                            {/each}
                        </Select>
                    </div>
                </div>
            </div>
        </form>
    {/if}
    
    <svelte:fragment slot="footer">
        <Button type="submit" color="blue" on:click={() => editForm.requestSubmit()}>{t('confirm')}</Button>
        <Button color="alternative" on:click={() => editModalOpen = false}>{t('cancel')}</Button>
    </svelte:fragment>
</Modal>